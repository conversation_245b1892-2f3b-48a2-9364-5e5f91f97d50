package com.example._06minio.client;

import com.example._06minio.common.Result;
import com.example._06minio.entity.FileInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * MinIO服务Feign客户端
 * 其他微服务可以通过此客户端调用MinIO服务
 */
@FeignClient(name = "minio-service", path = "/api/minio")
public interface MinioFeignClient {

    /**
     * 上传文件到默认存储桶
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<FileInfo> uploadFile(@RequestPart("file") MultipartFile file);

    /**
     * 上传文件到指定存储桶
     */
    @PostMapping(value = "/upload/{bucketName}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<FileInfo> uploadFile(@RequestPart("file") MultipartFile file, @PathVariable String bucketName);

    /**
     * 从默认存储桶删除文件
     */
    @DeleteMapping("/file/{objectName}")
    Result<Void> deleteFile(@PathVariable String objectName);

    /**
     * 从指定存储桶删除文件
     */
    @DeleteMapping("/file/{bucketName}/{objectName}")
    Result<Void> deleteFile(@PathVariable String bucketName, @PathVariable String objectName);

    /**
     * 列出默认存储桶中的所有文件
     */
    @GetMapping("/files")
    Result<List<FileInfo>> listFiles();

    /**
     * 列出指定存储桶中的所有文件
     */
    @GetMapping("/files/{bucketName}")
    Result<List<FileInfo>> listFiles(@PathVariable String bucketName);

    /**
     * 获取文件信息
     */
    @GetMapping("/file-info/{bucketName}/{objectName}")
    Result<FileInfo> getFileInfo(@PathVariable String bucketName, @PathVariable String objectName);

    /**
     * 获取预签名URL
     */
    @GetMapping("/presigned-url/{bucketName}/{objectName}")
    Result<String> getPresignedUrl(@PathVariable String bucketName, 
                                   @PathVariable String objectName,
                                   @RequestParam(defaultValue = "3600") int expires);

    /**
     * 创建存储桶
     */
    @PostMapping("/bucket/{bucketName}")
    Result<Void> createBucket(@PathVariable String bucketName);

    /**
     * 检查存储桶是否存在
     */
    @GetMapping("/bucket/{bucketName}/exists")
    Result<Boolean> bucketExists(@PathVariable String bucketName);

    /**
     * 获取所有存储桶
     */
    @GetMapping("/buckets")
    Result<List<String>> getAllBuckets();

    /**
     * 删除存储桶
     */
    @DeleteMapping("/bucket/{bucketName}")
    Result<Void> removeBucket(@PathVariable String bucketName);
}
