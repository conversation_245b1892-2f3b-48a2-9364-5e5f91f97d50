package com.example._05usingcommon.okhttp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * OkHttp模板类
 * 临时替代91项目的OkHttpTemplate
 */
@Component
public class OkHttpTemplate {

    private final OkHttpUtil okHttpUtil;
    private final ObjectMapper objectMapper;

    public OkHttpTemplate(OkHttpUtil okHttpUtil) {
        this.okHttpUtil = okHttpUtil;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * GET请求
     */
    public String get(String url) {
        try {
            return okHttpUtil.get(url);
        } catch (Exception e) {
            return "请求失败: " + e.getMessage();
        }
    }

    /**
     * GET请求，返回HttpResult
     */
    public HttpResult<String> getForResult(String url) {
        return okHttpUtil.getForResult(url);
    }

    /**
     * POST请求
     */
    public String post(String url, Object data) {
        try {
            String jsonBody = objectMapper.writeValueAsString(data);
            return okHttpUtil.postJson(url, jsonBody);
        } catch (Exception e) {
            return "请求失败: " + e.getMessage();
        }
    }

    /**
     * POST请求，返回HttpResult
     */
    public HttpResult<String> postForResult(String url, Object data) {
        try {
            String jsonBody = objectMapper.writeValueAsString(data);
            return okHttpUtil.postForResult(url, jsonBody);
        } catch (Exception e) {
            return HttpResult.failure(500, e.getMessage(), 0);
        }
    }

    /**
     * 创建测试数据
     */
    public Map<String, Object> createTestData() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "测试用户");
        data.put("age", 25);
        data.put("email", "<EMAIL>");
        return data;
    }

    /**
     * 获取状态信息
     */
    public String getStatus() {
        return "OkHttpTemplate已就绪，可以进行HTTP请求";
    }
}
