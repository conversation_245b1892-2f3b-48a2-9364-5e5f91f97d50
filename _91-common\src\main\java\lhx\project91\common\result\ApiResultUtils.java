package lhx.project91.common.result;

import lhx.project91.okhttp.model.HttpResult;

import java.util.List;
import java.util.function.Function;

/**
 * API结果工具类
 * 
 * <p>提供各种便捷的方法来创建和转换ApiResult：</p>
 * <ul>
 *   <li>条件判断创建结果</li>
 *   <li>异常安全的结果创建</li>
 *   <li>HttpResult转换</li>
 *   <li>分页结果创建</li>
 *   <li>批量操作结果处理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
public final class ApiResultUtils {

    private ApiResultUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 条件判断方法 ====================

    /**
     * 根据条件返回成功或失败结果
     * 
     * @param condition 判断条件
     * @param successData 成功时的数据
     * @param failureCode 失败时的结果码
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> condition(boolean condition, T successData, ResultCode failureCode) {
        return condition ? ApiResult.success(successData) : ApiResult.failure(failureCode);
    }

    /**
     * 根据条件返回成功或失败结果（带自定义消息）
     * 
     * @param condition 判断条件
     * @param successData 成功时的数据
     * @param successMessage 成功时的消息
     * @param failureCode 失败时的结果码
     * @param failureMessage 失败时的消息
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> condition(boolean condition, T successData, String successMessage,
                                           ResultCode failureCode, String failureMessage) {
        return condition 
            ? ApiResult.success(successData, successMessage)
            : ApiResult.failure(failureCode, failureMessage);
    }

    /**
     * 根据对象是否为null返回结果
     * 
     * @param data 数据对象
     * @param notFoundCode 对象为null时的结果码
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> ofNullable(T data, ResultCode notFoundCode) {
        return data != null ? ApiResult.success(data) : ApiResult.failure(notFoundCode);
    }

    /**
     * 根据对象是否为null返回结果（带自定义消息）
     * 
     * @param data 数据对象
     * @param notFoundCode 对象为null时的结果码
     * @param notFoundMessage 对象为null时的消息
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> ofNullable(T data, ResultCode notFoundCode, String notFoundMessage) {
        return data != null ? ApiResult.success(data) : ApiResult.failure(notFoundCode, notFoundMessage);
    }

    // ==================== 异常安全方法 ====================

    /**
     * 异常安全的结果创建
     * 
     * @param supplier 数据提供者
     * @param errorCode 异常时的结果码
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> tryCatch(ThrowableSupplier<T> supplier, ResultCode errorCode) {
        try {
            T data = supplier.get();
            return ApiResult.success(data);
        } catch (Exception e) {
            return ApiResult.failure(errorCode, errorCode.getMessage(), e.getMessage());
        }
    }

    /**
     * 异常安全的结果创建（带自定义错误消息）
     * 
     * @param supplier 数据提供者
     * @param errorCode 异常时的结果码
     * @param errorMessage 异常时的消息
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> tryCatch(ThrowableSupplier<T> supplier, ResultCode errorCode, String errorMessage) {
        try {
            T data = supplier.get();
            return ApiResult.success(data);
        } catch (Exception e) {
            return ApiResult.failure(errorCode, errorMessage, e.getMessage());
        }
    }

    // ==================== HttpResult转换方法 ====================

    /**
     * 将HttpResult转换为ApiResult
     * 
     * @param httpResult HttpResult对象
     * @param <T> 数据类型
     * @return ApiResult
     */
    public static <T> ApiResult<T> fromHttpResult(HttpResult<T> httpResult) {
        if (httpResult == null) {
            return ApiResult.failure(ResultCode.SYSTEM_ERROR, "HttpResult为空");
        }

        if (httpResult.isSuccessful()) {
            return ApiResult.success(httpResult.getData());
        } else {
            // 根据HTTP状态码映射到对应的ResultCode
            ResultCode resultCode = mapHttpStatusToResultCode(httpResult.getStatusCode());
            return ApiResult.<T>failure(resultCode, httpResult.getErrorMessage())
                    .withTraceId(httpResult.getUrl());
        }
    }

    /**
     * 将HttpResult转换为ApiResult（带数据转换）
     * 
     * @param httpResult HttpResult对象
     * @param converter 数据转换器
     * @param <S> 源数据类型
     * @param <T> 目标数据类型
     * @return ApiResult
     */
    public static <S, T> ApiResult<T> fromHttpResult(HttpResult<S> httpResult, Function<S, T> converter) {
        if (httpResult == null) {
            return ApiResult.failure(ResultCode.SYSTEM_ERROR, "HttpResult为空");
        }

        if (httpResult.isSuccessful() && httpResult.getData() != null) {
            try {
                T convertedData = converter.apply(httpResult.getData());
                return ApiResult.success(convertedData);
            } catch (Exception e) {
                return ApiResult.failure(ResultCode.SYSTEM_ERROR, "数据转换失败", e.getMessage());
            }
        } else {
            ResultCode resultCode = mapHttpStatusToResultCode(httpResult.getStatusCode());
            return ApiResult.failure(resultCode, httpResult.getErrorMessage());
        }
    }

    // ==================== 分页结果方法 ====================

    /**
     * 创建分页结果
     * 
     * @param records 数据列表
     * @param current 当前页码
     * @param size 每页大小
     * @param total 总记录数
     * @param <T> 数据类型
     * @return 分页结果的ApiResult
     */
    public static <T> ApiResult<PageData<T>> page(List<T> records, Long current, Long size, Long total) {
        PageData<T> pageData = PageData.of(records, current, size, total);
        return ApiResult.success(pageData);
    }

    /**
     * 创建分页结果（带消息）
     * 
     * @param records 数据列表
     * @param current 当前页码
     * @param size 每页大小
     * @param total 总记录数
     * @param message 自定义消息
     * @param <T> 数据类型
     * @return 分页结果的ApiResult
     */
    public static <T> ApiResult<PageData<T>> page(List<T> records, Long current, Long size, Long total, String message) {
        PageData<T> pageData = PageData.of(records, current, size, total);
        return ApiResult.success(pageData, message);
    }

    /**
     * 创建空分页结果
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param <T> 数据类型
     * @return 空分页结果的ApiResult
     */
    public static <T> ApiResult<PageData<T>> emptyPage(Long current, Long size) {
        PageData<T> pageData = PageData.empty(current, size);
        return ApiResult.success(pageData, "暂无数据");
    }

    // ==================== 批量操作方法 ====================

    /**
     * 处理批量操作结果
     * 
     * @param totalCount 总操作数
     * @param successCount 成功数
     * @param failureCount 失败数
     * @return ApiResult
     */
    public static ApiResult<BatchResult> batch(int totalCount, int successCount, int failureCount) {
        BatchResult batchResult = BatchResult.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .failureCount(failureCount)
                .build();

        if (failureCount == 0) {
            return ApiResult.success(batchResult, "批量操作全部成功");
        } else if (successCount == 0) {
            return ApiResult.failure(ResultCode.BUSINESS_ERROR, "批量操作全部失败");
        } else {
            return ApiResult.success(batchResult, "批量操作部分成功");
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 将HTTP状态码映射到ResultCode
     * 
     * @param httpStatus HTTP状态码
     * @return ResultCode
     */
    private static ResultCode mapHttpStatusToResultCode(int httpStatus) {
        switch (httpStatus) {
            case 400:
                return ResultCode.PARAM_ERROR;
            case 401:
                return ResultCode.UNAUTHORIZED;
            case 403:
                return ResultCode.FORBIDDEN;
            case 404:
                return ResultCode.NOT_FOUND;
            case 408:
                return ResultCode.TIMEOUT;
            case 409:
                return ResultCode.CONFLICT;
            case 429:
                return ResultCode.TOO_FREQUENT;
            case 500:
                return ResultCode.SYSTEM_ERROR;
            case 503:
                return ResultCode.SERVICE_UNAVAILABLE;
            case 504:
                return ResultCode.THIRD_PARTY_TIMEOUT;
            default:
                return httpStatus >= 500 ? ResultCode.SYSTEM_ERROR : ResultCode.BUSINESS_ERROR;
        }
    }

    // ==================== 函数式接口 ====================

    /**
     * 可抛出异常的Supplier接口
     * 
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface ThrowableSupplier<T> {
        /**
         * 获取结果
         * 
         * @return 结果
         * @throws Exception 可能抛出的异常
         */
        T get() throws Exception;
    }
}
