package com.example._03auth.config;

import com.example._03auth.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security安全配置类
 * 
 * 主要功能：
 * 1. 配置JWT认证过滤器
 * 2. 设置路径访问权限
 * 3. 禁用不需要的安全特性
 * 4. 配置认证管理器和密码编码器
 */
@Configuration
@EnableWebSecurity  // 启用Spring Security
@EnableGlobalMethodSecurity(prePostEnabled = true)  // 启用方法级别的权限控制，支持@PreAuthorize注解
public class SecurityConfig {
    
    /**
     * 注入自定义的JWT认证过滤器
     * 用于处理Bearer Token认证
     */
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;
    
    /**
     * 密码编码器Bean
     * 使用BCrypt算法对密码进行加密
     * 
     * @return BCryptPasswordEncoder实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 认证管理器Bean
     * Spring Security的核心组件，负责用户认证
     * 会自动发现UserDetailsService和PasswordEncoder
     * 
     * @param config 认证配置
     * @return AuthenticationManager实例
     * @throws Exception 配置异常
     */
    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
    
    /**
     * 安全过滤器链配置
     * 这是Spring Security的核心配置，定义了整个应用的安全策略
     * 
     * @param http HttpSecurity配置对象
     * @return SecurityFilterChain过滤器链
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
//        http.authorizeHttpRequests()
        http
            // 禁用CSRF保护
            // 因为我们使用JWT token，不需要CSRF保护
            // CSRF主要防护表单提交攻击，JWT是无状态的，不需要此保护
            .csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        http.authorizeHttpRequests(authz -> authz
                // 白名单：这些路径无需认证即可访问
                // permitAll()表示允许所有用户访问，包括未认证用户
                .antMatchers("/auth/login", "/auth/hello", "/auth/validate").permitAll()
                
                // 管理员专用接口：需要ADMIN角色
                // hasRole会自动添加"ROLE_"前缀，所以ADMIN对应ROLE_ADMIN
                // 只有拥有ROLE_ADMIN权限的用户才能访问/auth/admin/**路径
                .antMatchers("/auth/admin/**").hasRole("ADMIN")
                
                // 其他所有请求都需要认证
                // authenticated()表示用户必须已经通过认证（登录）
                .anyRequest().authenticated()
            )
            
            // 添加自定义JWT过滤器
            // 在UsernamePasswordAuthenticationFilter之前执行
            // 这样JWT认证会在表单认证之前处理
            // 过滤器执行顺序：JwtAuthenticationFilter → UsernamePasswordAuthenticationFilter → 其他过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
}


