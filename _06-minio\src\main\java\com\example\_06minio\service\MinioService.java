package com.example._06minio.service;

import com.example._06minio.entity.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * MinIO服务接口
 */
public interface MinioService {

    /**
     * 创建存储桶
     *
     * @param bucketName 存储桶名称
     */
    void createBucket(String bucketName);

    /**
     * 判断存储桶是否存在
     *
     * @param bucketName 存储桶名称
     * @return 是否存在
     */
    boolean bucketExists(String bucketName);

    /**
     * 获取所有存储桶
     *
     * @return 存储桶列表
     */
    List<String> getAllBuckets();

    /**
     * 删除存储桶
     *
     * @param bucketName 存储桶名称
     */
    void removeBucket(String bucketName);

    /**
     * 上传文件
     *
     * @param file       文件
     * @param bucketName 存储桶名称
     * @return 文件信息
     */
    FileInfo uploadFile(MultipartFile file, String bucketName);

    /**
     * 上传文件到默认存储桶
     *
     * @param file 文件
     * @return 文件信息
     */
    FileInfo uploadFile(MultipartFile file);

    /**
     * 上传文件流
     *
     * @param inputStream 文件流
     * @param fileName    文件名
     * @param contentType 文件类型
     * @param bucketName  存储桶名称
     * @return 文件信息
     */
    FileInfo uploadFile(InputStream inputStream, String fileName, String contentType, String bucketName);

    /**
     * 下载文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件流
     */
    InputStream downloadFile(String bucketName, String objectName);

    /**
     * 从默认存储桶下载文件
     *
     * @param objectName 对象名称
     * @return 文件流
     */
    InputStream downloadFile(String objectName);

    /**
     * 删除文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     */
    void deleteFile(String bucketName, String objectName);

    /**
     * 从默认存储桶删除文件
     *
     * @param objectName 对象名称
     */
    void deleteFile(String objectName);

    /**
     * 获取文件预签名URL
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expires    过期时间（秒）
     * @return 预签名URL
     */
    String getPresignedUrl(String bucketName, String objectName, int expires);

    /**
     * 获取文件信息
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件信息
     */
    FileInfo getFileInfo(String bucketName, String objectName);

    /**
     * 列出存储桶中的所有文件
     *
     * @param bucketName 存储桶名称
     * @return 文件列表
     */
    List<FileInfo> listFiles(String bucketName);

    /**
     * 列出默认存储桶中的所有文件
     *
     * @return 文件列表
     */
    List<FileInfo> listFiles();
}
