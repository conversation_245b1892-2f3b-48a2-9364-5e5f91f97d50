package com.example._03auth.controller;

import com.example._03auth.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private AuthenticationManager authenticationManager;

    @GetMapping("/hello")
    public String hello() {
        return "hello world";
    }

    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> loginData) {
        System.out.println("===============login ======================");
        String username = loginData.get("username");
        String password = loginData.get("password");
        
        try {
            // 使用Spring Security认证
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(username, password)
            );
            
            // 获取用户角色
            List<String> roles = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
            
            // 生成带角色的JWT
            String token = jwtUtil.generateToken(username, roles);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "登录成功");
            result.put("token", token);
            result.put("roles", roles);
            return result;
            
        } catch (AuthenticationException e) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 401);
            result.put("message", "用户名或密码错误");
            return result;
        }
    }
    
    // 保持原有validate接口，供网关调用
    @PostMapping("/validate")
    public Map<String, Object> validateToken(@RequestHeader("Authorization") String authHeader) {
        System.out.println("===============validate ======================");
        Map<String, Object> result = new HashMap<>();
        
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            if (jwtUtil.validateToken(token)) {
                String username = jwtUtil.getUsernameFromToken(token);
                List<String> roles = jwtUtil.getRolesFromToken(token);
                result.put("code", 200);
                result.put("valid", true);
                result.put("username", username);
                result.put("roles", roles);
                return result;
            }
        }
        
        result.put("code", 401);
        result.put("valid", false);
        return result;
    }
    
    // 新增：需要管理员权限的接口
    //    @PreAuthorize("hasAuthority('ROLE_ADMIN')")   // 完整格式
    @PreAuthorize("hasRole('ADMIN')")// 自动添加ROLE_前缀
    @GetMapping("/admin/info")
    public Map<String, Object> adminInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "管理员专用信息");
        result.put("data", "只有ADMIN角色才能看到这个信息");
        return result;
    }
    
    // 新增：普通用户接口
    //    @PreAuthorize("hasAuthority('ROLE_USER')")   // 完整格式
    @PreAuthorize("hasRole('USER')") // 简化格式，推荐 (自动添加ROLE_前缀)
    @GetMapping("/user/info")
    public Map<String, Object> userInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "用户信息");
        result.put("data", "USER角色可以看到这个信息");
        return result;
    }
}
