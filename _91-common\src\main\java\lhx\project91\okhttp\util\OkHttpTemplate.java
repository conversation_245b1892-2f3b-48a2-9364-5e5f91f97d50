package lhx.project91.okhttp.util;

import com.fasterxml.jackson.core.type.TypeReference;
import lhx.project91.okhttp.model.HttpResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * OkHttp模板工具类 - 类似RestTemplate的使用方式
 * 提供更高级的API，支持泛型返回和链式调用
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OkHttpTemplate {
    
    private final OkHttpUtil okHttpUtil;
    
    // ==================== GET 请求方法 ====================
    
    /**
     * GET请求 - 返回字符串
     * 
     * @param url 请求URL
     * @return HttpResult<String>
     */
    public HttpResult<String> getForString(String url) {
        return okHttpUtil.getForResult(url);
    }
    
    /**
     * GET请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return HttpResult<String>
     */
    public HttpResult<String> getForString(String url, Map<String, String> headers) {
        return okHttpUtil.getForResult(url, headers);
    }
    
    /**
     * GET请求 - 返回对象
     * 
     * @param url 请求URL
     * @param responseType 响应类型
     * @return HttpResult<T>
     */
    public <T> HttpResult<T> getForObject(String url, Class<T> responseType) {
        return okHttpUtil.getForObjectResult(url, responseType);
    }
    
    /**
     * GET请求 - 返回对象
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @return HttpResult<T>
     */
    public <T> HttpResult<T> getForObject(String url, Map<String, String> headers, Class<T> responseType) {
        return okHttpUtil.getForObjectResult(url, headers, responseType);
    }
    
    /**
     * GET请求 - 返回对象（支持泛型）
     * 
     * @param url 请求URL
     * @param typeReference 类型引用
     * @return HttpResult<T>
     */
    public <T> HttpResult<T> getForObject(String url, TypeReference<T> typeReference) {
        return getForObject(url, null, typeReference);
    }
    
    /**
     * GET请求 - 返回对象（支持泛型）
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param typeReference 类型引用
     * @return HttpResult<T>
     */
    public <T> HttpResult<T> getForObject(String url, Map<String, String> headers, TypeReference<T> typeReference) {
        HttpResult<String> stringResult = okHttpUtil.getForResult(url, headers);
        if (stringResult.isSuccessful()) {
            // 这里需要实现TypeReference的解析，暂时返回错误
            return HttpResult.failure(500, "TypeReference解析暂未实现");
        }
        return HttpResult.failure(stringResult.getStatusCode(), stringResult.getStatusMessage(), stringResult.getErrorMessage());
    }
    
    // ==================== POST 请求方法 ====================
    
    /**
     * POST请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @return HttpResult<String>
     */
    public HttpResult<String> postForString(String url, Object requestBody) {
        return postForString(url, requestBody, null);
    }
    
    /**
     * POST请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @param headers 请求头
     * @return HttpResult<String>
     */
    public HttpResult<String> postForString(String url, Object requestBody, Map<String, String> headers) {
        try {
            String response = okHttpUtil.postObject(url, requestBody, headers);
            return HttpResult.success(response);
        } catch (Exception e) {
            return HttpResult.failure(500, e.getMessage());
        }
    }
    
    /**
     * POST请求 - 返回对象
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @param responseType 响应类型
     * @return HttpResult<T>
     */
    public <T> HttpResult<T> postForObject(String url, Object requestBody, Class<T> responseType) {
        return postForObject(url, requestBody, null, responseType);
    }
    
    /**
     * POST请求 - 返回对象
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @param headers 请求头
     * @param responseType 响应类型
     * @return HttpResult<T>
     */
    public <T> HttpResult<T> postForObject(String url, Object requestBody, Map<String, String> headers, Class<T> responseType) {
        try {
            T response = okHttpUtil.postForObject(url, requestBody, headers, responseType);
            return HttpResult.success(response);
        } catch (Exception e) {
            return HttpResult.failure(500, e.getMessage());
        }
    }
    
    // ==================== PUT/DELETE 请求方法 ====================
    
    /**
     * PUT请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @return HttpResult<String>
     */
    public HttpResult<String> putForString(String url, Object requestBody) {
        return putForString(url, requestBody, null);
    }
    
    /**
     * PUT请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @param headers 请求头
     * @return HttpResult<String>
     */
    public HttpResult<String> putForString(String url, Object requestBody, Map<String, String> headers) {
        try {
            // 使用OkHttpUtil的postObject方法来处理对象转JSON
            String response = okHttpUtil.putJson(url, requestBody.toString(), headers);
            return HttpResult.success(response);
        } catch (Exception e) {
            return HttpResult.failure(500, e.getMessage());
        }
    }
    
    /**
     * DELETE请求 - 返回字符串
     * 
     * @param url 请求URL
     * @return HttpResult<String>
     */
    public HttpResult<String> deleteForString(String url) {
        return deleteForString(url, null);
    }
    
    /**
     * DELETE请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return HttpResult<String>
     */
    public HttpResult<String> deleteForString(String url, Map<String, String> headers) {
        return okHttpUtil.deleteForResult(url, headers);
    }
    
    // ==================== 异步请求方法 ====================
    
    /**
     * 异步GET请求
     * 
     * @param url 请求URL
     * @return CompletableFuture<HttpResult<String>>
     */
    public CompletableFuture<HttpResult<String>> getAsyncForString(String url) {
        return okHttpUtil.getAsyncFuture(url);
    }
    
    /**
     * 异步GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return CompletableFuture<HttpResult<String>>
     */
    public CompletableFuture<HttpResult<String>> getAsyncForString(String url, Map<String, String> headers) {
        return okHttpUtil.getAsyncFuture(url, headers);
    }
    
    /**
     * 异步POST请求
     * 
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @return CompletableFuture<HttpResult<String>>
     */
    public CompletableFuture<HttpResult<String>> postAsyncForString(String url, Object requestBody) {
        try {
            // 使用OkHttpUtil的postObject方法来处理对象转JSON
            String response = okHttpUtil.postObject(url, requestBody);
            CompletableFuture<HttpResult<String>> future = new CompletableFuture<>();
            future.complete(HttpResult.success(response));
            return future;
        } catch (Exception e) {
            CompletableFuture<HttpResult<String>> future = new CompletableFuture<>();
            future.complete(HttpResult.failure(500, e.getMessage()));
            return future;
        }
    }
}
