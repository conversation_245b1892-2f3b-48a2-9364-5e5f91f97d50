package lhx.project91.okhttp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * OkHttp 配置属性
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "okhttp")
public class OkHttpProperties {

    /**
     * 是否启用 OkHttp
     */
    private boolean enabled = true;

    /**
     * 连接超时时间（秒）
     */
    private Duration connectTimeout = Duration.ofSeconds(10);

    /**
     * 读取超时时间（秒）
     */
    private Duration readTimeout = Duration.ofSeconds(30);

    /**
     * 写入超时时间（秒）
     */
    private Duration writeTimeout = Duration.ofSeconds(30);

    /**
     * 调用超时时间（秒）
     */
    private Duration callTimeout = Duration.ofSeconds(60);

    /**
     * 连接池配置
     */
    private ConnectionPool connectionPool = new ConnectionPool();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 日志配置
     */
    private Logging logging = new Logging();

    /**
     * 连接池配置
     */
    @Data
    public static class ConnectionPool {
        /**
         * 最大空闲连接数
         */
        private int maxIdleConnections = 5;

        /**
         * 连接保持活跃时间（分钟）
         */
        private Duration keepAliveDuration = Duration.ofMinutes(5);
    }

    /**
     * 重试配置
     */
    @Data
    public static class Retry {
        /**
         * 是否启用重试
         */
        private boolean enabled = true;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 重试间隔（毫秒）
         */
        private Duration retryInterval = Duration.ofMillis(1000);
    }

    /**
     * 日志配置
     */
    @Data
    public static class Logging {
        /**
         * 是否启用请求日志
         */
        private boolean enabled = true;

        /**
         * 日志级别：NONE, BASIC, HEADERS, BODY
         */
        private Level level = Level.BASIC;

        public enum Level {
            NONE, BASIC, HEADERS, BODY
        }
    }
}
