package com.example._05usingcommon.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 简化的OkHttp工具类
 * 用于替代91项目的OkHttpUtil，避免依赖冲突
 */
@Component
public class SimpleOkHttpUtil {

    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;

    public SimpleOkHttpUtil() {
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * GET请求
     */
    public String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                return response.body().string();
            }
            return "";
        }
    }

    /**
     * POST JSON请求
     */
    public String postJson(String url, String jsonBody) throws IOException {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                return response.body().string();
            }
            return "";
        }
    }

    /**
     * POST对象请求
     */
    public String postObject(String url, Object obj) throws IOException {
        String jsonBody = objectMapper.writeValueAsString(obj);
        return postJson(url, jsonBody);
    }
}
