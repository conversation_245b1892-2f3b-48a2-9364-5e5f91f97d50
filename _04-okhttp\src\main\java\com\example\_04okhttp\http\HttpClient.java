package com.example._04okhttp.http;

import com.example._04okhttp.http.interceptor.LoggingInterceptor;
import com.example._04okhttp.http.interceptor.RetryInterceptor;
import okhttp3.*;
import org.springframework.stereotype.Component;
import java.util.concurrent.TimeUnit;

/**
 * HTTP客户端核心类
 * 基于OkHttp封装，提供简洁易用的API
 */
@Component
public class HttpClient {
    
    private final OkHttpClient okHttpClient;
    
    public HttpClient() {
        this.okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .addInterceptor(new LoggingInterceptor())
            .addInterceptor(new RetryInterceptor())
            .build();
    }
    
    /**
     * GET请求
     */
    public HttpRequest get(String url) {
        return new HttpRequest(okHttpClient, "GET", url);
    }
    
    /**
     * POST请求
     */
    public HttpRequest post(String url) {
        return new HttpRequest(okHttpClient, "POST", url);
    }
    
    /**
     * PUT请求
     */
    public HttpRequest put(String url) {
        return new HttpRequest(okHttpClient, "PUT", url);
    }
    
    /**
     * DELETE请求
     */
    public HttpRequest delete(String url) {
        return new HttpRequest(okHttpClient, "DELETE", url);
    }
}