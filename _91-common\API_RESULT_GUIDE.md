# ApiResult 统一响应结果使用指南

## 📋 **概述**

`ApiResult` 是一个企业级的统一API响应结果封装类，提供了完整的响应数据封装、错误处理、分页支持等功能。

## 🏗️ **包结构**

```
lhx.project91.common.result/
├── ApiResult.java              # 核心响应结果类
├── ResultCode.java             # 统一结果码枚举
├── PageData.java               # 分页数据封装
├── ApiResultUtils.java         # 工具类
├── BatchResult.java            # 批量操作结果
├── BusinessException.java      # 业务异常类
├── GlobalResponseHandler.java  # 全局响应处理器
└── GlobalExceptionHandler.java # 全局异常处理器
```

## 🚀 **基本使用**

### 1. 成功响应

```java
@RestController
public class UserController {
    
    // 返回成功结果（无数据）
    @PostMapping("/users/{id}/activate")
    public ApiResult<Void> activateUser(@PathVariable Long id) {
        userService.activate(id);
        return ApiResult.success();
    }
    
    // 返回成功结果（带数据）
    @GetMapping("/users/{id}")
    public ApiResult<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return ApiResult.success(user);
    }
    
    // 返回成功结果（带自定义消息）
    @PostMapping("/users")
    public ApiResult<User> createUser(@RequestBody User user) {
        User savedUser = userService.save(user);
        return ApiResult.success(savedUser, "用户创建成功");
    }
}
```

### 2. 失败响应

```java
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public ApiResult<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        if (user == null) {
            return ApiResult.failure(ResultCode.NOT_FOUND, "用户不存在");
        }
        return ApiResult.success(user);
    }
    
    @PostMapping("/users")
    public ApiResult<User> createUser(@RequestBody User user) {
        if (userService.existsByEmail(user.getEmail())) {
            return ApiResult.failure(ResultCode.ALREADY_EXISTS, "邮箱已被使用");
        }
        User savedUser = userService.save(user);
        return ApiResult.success(savedUser);
    }
}
```

### 3. 分页响应

```java
@RestController
public class UserController {
    
    @GetMapping("/users")
    public ApiResult<PageData<User>> getUsers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        
        List<User> users = userService.findUsers(current, size);
        long total = userService.countUsers();
        
        PageData<User> pageData = PageData.of(users, current, size, total);
        return ApiResult.success(pageData);
    }
    
    // 使用工具类简化
    @GetMapping("/users/simple")
    public ApiResult<PageData<User>> getUsersSimple(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        
        List<User> users = userService.findUsers(current, size);
        long total = userService.countUsers();
        
        return ApiResultUtils.page(users, current, size, total, "查询成功");
    }
}
```

## 🛠️ **工具类使用**

### 1. 条件判断

```java
@Service
public class UserService {
    
    public ApiResult<User> updateUser(Long id, User user) {
        User existingUser = userRepository.findById(id);
        
        // 条件判断创建结果
        return ApiResultUtils.condition(
            existingUser != null,
            userRepository.save(user),
            ResultCode.NOT_FOUND
        );
    }
    
    public ApiResult<User> findUser(Long id) {
        User user = userRepository.findById(id);
        
        // 空值判断
        return ApiResultUtils.ofNullable(user, ResultCode.NOT_FOUND, "用户不存在");
    }
}
```

### 2. 异常安全

```java
@Service
public class FileService {
    
    public ApiResult<String> processFile(String filePath) {
        // 异常安全的操作
        return ApiResultUtils.tryCatch(
            () -> {
                // 可能抛出异常的操作
                return fileProcessor.process(filePath);
            },
            ResultCode.FILE_UPLOAD_FAILED,
            "文件处理失败"
        );
    }
}
```

### 3. HttpResult转换

```java
@Service
public class ExternalApiService {
    
    @Autowired
    private OkHttpUtil okHttpUtil;
    
    public ApiResult<ExternalData> getExternalData(String id) {
        HttpResult<String> httpResult = okHttpUtil.getForResult("https://api.example.com/data/" + id);
        
        // 转换HttpResult为ApiResult
        return ApiResultUtils.fromHttpResult(httpResult, json -> {
            // 自定义数据转换逻辑
            return objectMapper.readValue(json, ExternalData.class);
        });
    }
}
```

## 🎯 **业务异常处理**

### 1. 抛出业务异常

```java
@Service
public class OrderService {
    
    public void cancelOrder(Long orderId) {
        Order order = orderRepository.findById(orderId);
        
        // 使用断言方法
        BusinessException.assertNotNull(order, ResultCode.NOT_FOUND, "订单不存在");
        BusinessException.assertTrue(order.canCancel(), ResultCode.STATUS_ERROR, "订单状态不允许取消");
        
        // 或者直接抛出异常
        if (order.isPaid()) {
            throw BusinessException.businessError("已支付订单无法取消");
        }
        
        order.cancel();
        orderRepository.save(order);
    }
}
```

### 2. 全局异常处理

系统会自动捕获 `BusinessException` 并转换为标准的 `ApiResult` 格式：

```json
{
  "success": false,
  "httpStatus": 404,
  "code": "4000",
  "message": "订单不存在",
  "errorDetail": null,
  "traceId": "trace-123456",
  "timestamp": "2024-01-01 12:00:00"
}
```

## 📊 **批量操作**

```java
@Service
public class BatchUserService {
    
    public ApiResult<BatchResult> batchDeleteUsers(List<Long> userIds) {
        long startTime = System.currentTimeMillis();
        BatchResult batchResult = BatchResult.builder()
                .totalCount(userIds.size())
                .successCount(0)
                .failureCount(0)
                .build();
        
        for (Long userId : userIds) {
            try {
                userService.deleteUser(userId);
                batchResult.addSuccessCount(1);
            } catch (Exception e) {
                batchResult.addFailureCount(1)
                          .addFailureDetail(userId.toString(), e.getMessage());
            }
        }
        
        batchResult.setDuration(startTime, System.currentTimeMillis());
        
        if (batchResult.isAllSuccess()) {
            return ApiResult.success(batchResult, "批量删除成功");
        } else if (batchResult.isAllFailure()) {
            return ApiResult.failure(ResultCode.BUSINESS_ERROR, "批量删除失败");
        } else {
            return ApiResult.success(batchResult, "批量删除部分成功");
        }
    }
}
```

## 🔧 **配置选项**

在 `application.yml` 中可以配置：

```yaml
okhttp:
  # 是否启用全局响应处理器（默认：true）
  global-response-handler:
    enabled: true
  
  # 是否启用全局异常处理器（默认：true）
  global-exception-handler:
    enabled: true
```

## 📝 **响应格式**

### 成功响应示例

```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "traceId": "trace-123456",
  "timestamp": "2024-01-01 12:00:00",
  "server": "server-01",
  "version": "v1.0"
}
```

### 分页响应示例

```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "查询成功",
  "data": {
    "records": [
      {"id": 1, "name": "用户1"},
      {"id": 2, "name": "用户2"}
    ],
    "current": 1,
    "size": 10,
    "total": 100,
    "pages": 10,
    "hasPrevious": false,
    "hasNext": true,
    "isFirst": true,
    "isLast": false,
    "currentSize": 2
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 失败响应示例

```json
{
  "success": false,
  "httpStatus": 404,
  "code": "4000",
  "message": "用户不存在",
  "errorDetail": "数据库中未找到ID为123的用户记录",
  "traceId": "trace-123456",
  "timestamp": "2024-01-01 12:00:00"
}
```

## 🎨 **最佳实践**

### 1. Controller层

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    // 推荐：直接返回业务数据，由全局处理器自动包装
    @GetMapping("/{id}")
    public User getUser(@PathVariable Long id) {
        return userService.findById(id);
    }
    
    // 或者：手动包装为ApiResult（当需要自定义消息时）
    @PostMapping
    public ApiResult<User> createUser(@RequestBody User user) {
        User savedUser = userService.save(user);
        return ApiResult.success(savedUser, "用户创建成功");
    }
}
```

### 2. Service层

```java
@Service
public class UserService {
    
    // 推荐：抛出BusinessException，由全局异常处理器处理
    public User findById(Long id) {
        User user = userRepository.findById(id);
        if (user == null) {
            throw BusinessException.notFound("用户不存在");
        }
        return user;
    }
    
    // 或者：返回ApiResult（当需要更细粒度控制时）
    public ApiResult<User> findByIdWithResult(Long id) {
        User user = userRepository.findById(id);
        return ApiResultUtils.ofNullable(user, ResultCode.NOT_FOUND, "用户不存在");
    }
}
```

### 3. 错误处理

```java
// 推荐的错误处理方式
@Service
public class OrderService {
    
    public void processOrder(Long orderId) {
        Order order = findOrderById(orderId);
        
        // 使用断言进行参数校验
        BusinessException.assertTrue(order.canProcess(), 
            ResultCode.STATUS_ERROR, "订单状态不允许处理");
        
        try {
            // 业务处理
            order.process();
            orderRepository.save(order);
        } catch (Exception e) {
            // 包装为业务异常
            throw BusinessException.of(ResultCode.BUSINESS_ERROR, "订单处理失败", e.getMessage());
        }
    }
    
    private Order findOrderById(Long orderId) {
        return orderRepository.findById(orderId)
            .orElseThrow(() -> BusinessException.notFound("订单不存在"));
    }
}
```

## 🔄 **与现有HttpResult的关系**

- `ApiResult` 是更高级的响应封装，适用于Web API
- `HttpResult` 专注于HTTP客户端调用结果
- 可以使用 `ApiResultUtils.fromHttpResult()` 进行转换
- 建议新项目使用 `ApiResult`，现有项目可以逐步迁移

这个设计提供了完整的企业级API响应解决方案，支持各种使用场景，并且易于扩展和维护。
