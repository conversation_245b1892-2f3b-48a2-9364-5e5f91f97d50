@echo off
echo 启动MinIO服务器...
echo.

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未在PATH中
    echo 请先安装Docker Desktop
    pause
    exit /b 1
)

echo 正在启动MinIO容器...
docker run -d -p 9000:9000 -p 9001:9001 ^
  --name minio ^
  -e "MINIO_ROOT_USER=minioadmin" ^
  -e "MINIO_ROOT_PASSWORD=minioadmin" ^
  minio/minio server /data --console-address ":9001"

if %errorlevel% equ 0 (
    echo.
    echo MinIO服务器启动成功！
    echo.
    echo MinIO API地址: http://localhost:9000
    echo MinIO控制台: http://localhost:9001
    echo 用户名: minioadmin
    echo 密码: minioadmin
    echo.
    echo 等待MinIO服务器完全启动...
    timeout /t 10 /nobreak >nul
    echo.
    echo 现在可以启动Spring Boot应用了
    echo 运行命令: mvn spring-boot:run
    echo 或者在IDE中运行Application.java
) else (
    echo.
    echo MinIO容器启动失败，可能是容器已存在
    echo 尝试启动现有容器...
    docker start minio
    if %errorlevel% equ 0 (
        echo MinIO容器启动成功！
    ) else (
        echo 启动失败，请检查Docker状态
    )
)

echo.
pause
