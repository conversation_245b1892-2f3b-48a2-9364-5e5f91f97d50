package lhx.project91.common.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量操作结果封装类
 * 
 * <p>用于封装批量操作的执行结果，包含：</p>
 * <ul>
 *   <li>操作统计信息（总数、成功数、失败数）</li>
 *   <li>失败详情列表</li>
 *   <li>操作成功率计算</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>{@code
 * // 批量删除用户
 * BatchResult result = BatchResult.builder()
 *     .totalCount(100)
 *     .successCount(95)
 *     .failureCount(5)
 *     .build();
 * 
 * // 添加失败详情
 * result.addFailureDetail("用户ID:123", "用户不存在");
 * result.addFailureDetail("用户ID:456", "用户已被删除");
 * 
 * return ApiResult.success(result);
 * }</pre>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总操作数
     */
    @Builder.Default
    private Integer totalCount = 0;

    /**
     * 成功数
     */
    @Builder.Default
    private Integer successCount = 0;

    /**
     * 失败数
     */
    @Builder.Default
    private Integer failureCount = 0;

    /**
     * 成功率（百分比）
     */
    private Double successRate;

    /**
     * 失败详情列表
     */
    @Builder.Default
    private List<FailureDetail> failureDetails = new ArrayList<>();

    /**
     * 操作耗时（毫秒）
     */
    private Long duration;

    /**
     * 失败详情内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FailureDetail implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 失败项标识（如ID、索引等）
         */
        private String identifier;

        /**
         * 失败原因
         */
        private String reason;

        /**
         * 错误码
         */
        private String errorCode;

        /**
         * 失败时间戳
         */
        private Long timestamp;

        /**
         * 创建失败详情
         * 
         * @param identifier 失败项标识
         * @param reason 失败原因
         * @return FailureDetail
         */
        public static FailureDetail of(String identifier, String reason) {
            return FailureDetail.builder()
                    .identifier(identifier)
                    .reason(reason)
                    .timestamp(System.currentTimeMillis())
                    .build();
        }

        /**
         * 创建失败详情（带错误码）
         * 
         * @param identifier 失败项标识
         * @param reason 失败原因
         * @param errorCode 错误码
         * @return FailureDetail
         */
        public static FailureDetail of(String identifier, String reason, String errorCode) {
            return FailureDetail.builder()
                    .identifier(identifier)
                    .reason(reason)
                    .errorCode(errorCode)
                    .timestamp(System.currentTimeMillis())
                    .build();
        }
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建全部成功的批量结果
     * 
     * @param totalCount 总数
     * @return BatchResult
     */
    public static BatchResult allSuccess(Integer totalCount) {
        return BatchResult.builder()
                .totalCount(totalCount)
                .successCount(totalCount)
                .failureCount(0)
                .build()
                .calculateSuccessRate();
    }

    /**
     * 创建全部失败的批量结果
     * 
     * @param totalCount 总数
     * @return BatchResult
     */
    public static BatchResult allFailure(Integer totalCount) {
        return BatchResult.builder()
                .totalCount(totalCount)
                .successCount(0)
                .failureCount(totalCount)
                .build()
                .calculateSuccessRate();
    }

    /**
     * 创建批量结果
     * 
     * @param totalCount 总数
     * @param successCount 成功数
     * @param failureCount 失败数
     * @return BatchResult
     */
    public static BatchResult of(Integer totalCount, Integer successCount, Integer failureCount) {
        return BatchResult.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .failureCount(failureCount)
                .build()
                .calculateSuccessRate();
    }

    // ==================== 计算方法 ====================

    /**
     * 计算成功率
     * 
     * @return 当前BatchResult实例（支持链式调用）
     */
    public BatchResult calculateSuccessRate() {
        if (totalCount != null && totalCount > 0) {
            this.successRate = (double) (successCount != null ? successCount : 0) / totalCount * 100;
            // 保留两位小数
            this.successRate = Math.round(this.successRate * 100.0) / 100.0;
        } else {
            this.successRate = 0.0;
        }
        return this;
    }

    /**
     * 重新计算统计信息
     * 
     * @return 当前BatchResult实例
     */
    public BatchResult recalculate() {
        if (successCount != null && failureCount != null) {
            this.totalCount = this.successCount + this.failureCount;
        }
        return calculateSuccessRate();
    }

    // ==================== 便捷方法 ====================

    /**
     * 添加失败详情
     * 
     * @param identifier 失败项标识
     * @param reason 失败原因
     * @return 当前BatchResult实例
     */
    public BatchResult addFailureDetail(String identifier, String reason) {
        if (this.failureDetails == null) {
            this.failureDetails = new ArrayList<>();
        }
        this.failureDetails.add(FailureDetail.of(identifier, reason));
        return this;
    }

    /**
     * 添加失败详情（带错误码）
     * 
     * @param identifier 失败项标识
     * @param reason 失败原因
     * @param errorCode 错误码
     * @return 当前BatchResult实例
     */
    public BatchResult addFailureDetail(String identifier, String reason, String errorCode) {
        if (this.failureDetails == null) {
            this.failureDetails = new ArrayList<>();
        }
        this.failureDetails.add(FailureDetail.of(identifier, reason, errorCode));
        return this;
    }

    /**
     * 添加失败详情
     * 
     * @param failureDetail 失败详情
     * @return 当前BatchResult实例
     */
    public BatchResult addFailureDetail(FailureDetail failureDetail) {
        if (this.failureDetails == null) {
            this.failureDetails = new ArrayList<>();
        }
        this.failureDetails.add(failureDetail);
        return this;
    }

    /**
     * 增加成功数
     * 
     * @param count 增加的数量
     * @return 当前BatchResult实例
     */
    public BatchResult addSuccessCount(Integer count) {
        this.successCount = (this.successCount != null ? this.successCount : 0) + (count != null ? count : 0);
        return recalculate();
    }

    /**
     * 增加失败数
     * 
     * @param count 增加的数量
     * @return 当前BatchResult实例
     */
    public BatchResult addFailureCount(Integer count) {
        this.failureCount = (this.failureCount != null ? this.failureCount : 0) + (count != null ? count : 0);
        return recalculate();
    }

    /**
     * 设置操作耗时
     * 
     * @param startTime 开始时间（毫秒）
     * @param endTime 结束时间（毫秒）
     * @return 当前BatchResult实例
     */
    public BatchResult setDuration(Long startTime, Long endTime) {
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
        return this;
    }

    // ==================== 判断方法 ====================

    /**
     * 判断是否全部成功
     * 
     * @return true表示全部成功
     */
    public boolean isAllSuccess() {
        return failureCount != null && failureCount == 0 && successCount != null && successCount > 0;
    }

    /**
     * 判断是否全部失败
     * 
     * @return true表示全部失败
     */
    public boolean isAllFailure() {
        return successCount != null && successCount == 0 && failureCount != null && failureCount > 0;
    }

    /**
     * 判断是否部分成功
     * 
     * @return true表示部分成功
     */
    public boolean isPartialSuccess() {
        return successCount != null && successCount > 0 && failureCount != null && failureCount > 0;
    }

    /**
     * 判断是否有失败详情
     * 
     * @return true表示有失败详情
     */
    public boolean hasFailureDetails() {
        return failureDetails != null && !failureDetails.isEmpty();
    }

    /**
     * 获取操作结果摘要
     * 
     * @return 操作结果摘要字符串
     */
    public String getSummary() {
        return String.format("批量操作完成：总数%d，成功%d，失败%d，成功率%.2f%%",
                totalCount != null ? totalCount : 0,
                successCount != null ? successCount : 0,
                failureCount != null ? failureCount : 0,
                successRate != null ? successRate : 0.0);
    }
}
