package com.example._06minio;

import com.example._06minio.entity.FileInfo;
import com.example._06minio.service.MinioService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

/**
 * MinIO服务测试类
 * 注意：运行此测试需要MinIO服务器正在运行
 */
@SpringBootTest
@TestPropertySource(properties = {
    "minio.endpoint=http://localhost:9000",
    "minio.access-key=minioadmin",
    "minio.secret-key=minioadmin",
    "minio.bucket-name=test-bucket"
})
public class MinioServiceTest {

    @Autowired
    private MinioService minioService;

    @Test
    public void testCreateBucket() {
        try {
            minioService.createBucket("test-bucket");
            System.out.println("存储桶创建成功");
        } catch (Exception e) {
            System.out.println("存储桶创建失败或已存在: " + e.getMessage());
        }
    }

    @Test
    public void testBucketExists() {
        try {
            boolean exists = minioService.bucketExists("test-bucket");
            System.out.println("存储桶是否存在: " + exists);
        } catch (Exception e) {
            System.out.println("检查存储桶失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetAllBuckets() {
        try {
            List<String> buckets = minioService.getAllBuckets();
            System.out.println("所有存储桶: " + buckets);
        } catch (Exception e) {
            System.out.println("获取存储桶列表失败: " + e.getMessage());
        }
    }

    @Test
    public void testUploadFile() {
        try {
            // 创建模拟文件
            MockMultipartFile file = new MockMultipartFile(
                "test.txt", 
                "test.txt", 
                "text/plain", 
                "Hello MinIO!".getBytes()
            );
            
            FileInfo fileInfo = minioService.uploadFile(file);
            System.out.println("文件上传成功: " + fileInfo);
        } catch (Exception e) {
            System.out.println("文件上传失败: " + e.getMessage());
        }
    }

    @Test
    public void testListFiles() {
        try {
            List<FileInfo> files = minioService.listFiles();
            System.out.println("文件列表: " + files);
        } catch (Exception e) {
            System.out.println("获取文件列表失败: " + e.getMessage());
        }
    }
}
