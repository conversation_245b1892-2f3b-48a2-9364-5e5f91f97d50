package com.example._05usingcommon.config;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 临时的全局响应处理配置
 * 用于测试自动封装功能
 */
public class GlobalResponseConfig {

    /**
     * 简化的ApiResult类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SimpleApiResult<T> implements Serializable {
        private static final long serialVersionUID = 1L;

        private Boolean success;
        private Integer httpStatus;
        private String code;
        private String message;
        private T data;
        private String traceId;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        
        private String server;
        private String version;

        public static <T> SimpleApiResult<T> success(T data) {
            return SimpleApiResult.<T>builder()
                    .success(true)
                    .httpStatus(200)
                    .code("1000")
                    .message("操作成功")
                    .data(data)
                    .timestamp(LocalDateTime.now())
                    .server("using-common-server")
                    .version("v1.0")
                    .build();
        }

        public static <T> SimpleApiResult<T> success() {
            return success(null);
        }
    }

    /**
     * 全局响应处理器
     */
    @RestControllerAdvice
    public static class SimpleGlobalResponseHandler implements ResponseBodyAdvice<Object> {

        @Override
        public boolean supports(@NonNull MethodParameter returnType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
            // 排除已经是SimpleApiResult类型的返回值
            Class<?> parameterType = returnType.getParameterType();
            if (SimpleApiResult.class.isAssignableFrom(parameterType)) {
                return false;
            }
            
            // 排除错误页面相关的响应
            String declaringClassName = returnType.getDeclaringClass().getName();
            if (declaringClassName.contains("BasicErrorController") ||
                declaringClassName.contains("ErrorController")) {
                return false;
            }
            
            return true;
        }

        @Override
        public Object beforeBodyWrite(@Nullable Object body, @NonNull MethodParameter returnType, @NonNull MediaType selectedContentType,
                                    @NonNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                    @NonNull ServerHttpRequest request, @NonNull ServerHttpResponse response) {
            
            // 如果已经是SimpleApiResult类型，直接返回
            if (body instanceof SimpleApiResult) {
                return body;
            }
            
            // 处理void类型或null返回值
            if (body == null) {
                return SimpleApiResult.success();
            }
            
            // 处理String类型
            if (body instanceof String) {
                String stringBody = (String) body;
                // 如果是JSON格式的ApiResult字符串，直接返回
                if (stringBody.trim().startsWith("{") && stringBody.contains("\"success\"")) {
                    return stringBody;
                }
                return SimpleApiResult.success(stringBody);
            }
            
            // 处理基本数据类型的包装类
            if (isPrimitiveWrapper(body.getClass())) {
                return SimpleApiResult.success(body);
            }
            
            // 处理集合类型
            if (body instanceof java.util.Collection || body instanceof java.util.Map) {
                return SimpleApiResult.success(body);
            }
            
            // 处理数组类型
            if (body.getClass().isArray()) {
                return SimpleApiResult.success(body);
            }
            
            // 其他POJO类型自动包装为成功结果
            return SimpleApiResult.success(body);
        }

        /**
         * 判断是否为基本数据类型的包装类
         */
        private boolean isPrimitiveWrapper(Class<?> clazz) {
            return clazz == Boolean.class ||
                   clazz == Byte.class ||
                   clazz == Character.class ||
                   clazz == Short.class ||
                   clazz == Integer.class ||
                   clazz == Long.class ||
                   clazz == Float.class ||
                   clazz == Double.class;
        }
    }
}
