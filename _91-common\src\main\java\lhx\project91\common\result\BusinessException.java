package lhx.project91.common.result;

import lombok.Getter;

/**
 * 业务异常类
 * 
 * <p>用于封装业务逻辑中的异常情况，包含：</p>
 * <ul>
 *   <li>结果码信息</li>
 *   <li>错误消息</li>
 *   <li>错误详情</li>
 *   <li>便捷的创建方法</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>{@code
 * // 抛出业务异常
 * if (user == null) {
 *     throw BusinessException.of(ResultCode.NOT_FOUND, "用户不存在");
 * }
 * 
 * // 带详细信息的异常
 * throw BusinessException.of(ResultCode.BUSINESS_ERROR, "订单状态错误", 
 *     "当前订单状态为: " + order.getStatus());
 * }</pre>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Getter
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 结果码
     */
    private final ResultCode resultCode;

    /**
     * 错误详情
     */
    private final String errorDetail;

    /**
     * 构造函数
     * 
     * @param resultCode 结果码
     */
    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.resultCode = resultCode;
        this.errorDetail = null;
    }

    /**
     * 构造函数
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     */
    public BusinessException(ResultCode resultCode, String message) {
        super(message);
        this.resultCode = resultCode;
        this.errorDetail = null;
    }

    /**
     * 构造函数
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @param errorDetail 错误详情
     */
    public BusinessException(ResultCode resultCode, String message, String errorDetail) {
        super(message);
        this.resultCode = resultCode;
        this.errorDetail = errorDetail;
    }

    /**
     * 构造函数
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(ResultCode resultCode, String message, Throwable cause) {
        super(message, cause);
        this.resultCode = resultCode;
        this.errorDetail = cause != null ? cause.getMessage() : null;
    }

    /**
     * 构造函数
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @param errorDetail 错误详情
     * @param cause 原因异常
     */
    public BusinessException(ResultCode resultCode, String message, String errorDetail, Throwable cause) {
        super(message, cause);
        this.resultCode = resultCode;
        this.errorDetail = errorDetail;
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建业务异常
     * 
     * @param resultCode 结果码
     * @return BusinessException
     */
    public static BusinessException of(ResultCode resultCode) {
        return new BusinessException(resultCode);
    }

    /**
     * 创建业务异常
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException of(ResultCode resultCode, String message) {
        return new BusinessException(resultCode, message);
    }

    /**
     * 创建业务异常
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @param errorDetail 错误详情
     * @return BusinessException
     */
    public static BusinessException of(ResultCode resultCode, String message, String errorDetail) {
        return new BusinessException(resultCode, message, errorDetail);
    }

    /**
     * 创建业务异常
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @param cause 原因异常
     * @return BusinessException
     */
    public static BusinessException of(ResultCode resultCode, String message, Throwable cause) {
        return new BusinessException(resultCode, message, cause);
    }

    /**
     * 创建业务异常
     * 
     * @param resultCode 结果码
     * @param message 错误消息
     * @param errorDetail 错误详情
     * @param cause 原因异常
     * @return BusinessException
     */
    public static BusinessException of(ResultCode resultCode, String message, String errorDetail, Throwable cause) {
        return new BusinessException(resultCode, message, errorDetail, cause);
    }

    // ==================== 便捷的静态方法 ====================

    /**
     * 参数错误异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException paramError(String message) {
        return new BusinessException(ResultCode.PARAM_ERROR, message);
    }

    /**
     * 资源不存在异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(ResultCode.NOT_FOUND, message);
    }

    /**
     * 未认证异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(ResultCode.UNAUTHORIZED, message);
    }

    /**
     * 无权限异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(ResultCode.FORBIDDEN, message);
    }

    /**
     * 资源冲突异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException conflict(String message) {
        return new BusinessException(ResultCode.CONFLICT, message);
    }

    /**
     * 业务处理异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException businessError(String message) {
        return new BusinessException(ResultCode.BUSINESS_ERROR, message);
    }

    /**
     * 系统错误异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException systemError(String message) {
        return new BusinessException(ResultCode.SYSTEM_ERROR, message);
    }

    /**
     * 操作频繁异常
     * 
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException tooFrequent(String message) {
        return new BusinessException(ResultCode.TOO_FREQUENT, message);
    }

    // ==================== 条件判断方法 ====================

    /**
     * 断言条件为真，否则抛出异常
     * 
     * @param condition 条件
     * @param resultCode 结果码
     * @param message 错误消息
     */
    public static void assertTrue(boolean condition, ResultCode resultCode, String message) {
        if (!condition) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言条件为假，否则抛出异常
     * 
     * @param condition 条件
     * @param resultCode 结果码
     * @param message 错误消息
     */
    public static void assertFalse(boolean condition, ResultCode resultCode, String message) {
        if (condition) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言对象不为null，否则抛出异常
     * 
     * @param object 对象
     * @param resultCode 结果码
     * @param message 错误消息
     */
    public static void assertNotNull(Object object, ResultCode resultCode, String message) {
        if (object == null) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言对象为null，否则抛出异常
     * 
     * @param object 对象
     * @param resultCode 结果码
     * @param message 错误消息
     */
    public static void assertNull(Object object, ResultCode resultCode, String message) {
        if (object != null) {
            throw new BusinessException(resultCode, message);
        }
    }
}
