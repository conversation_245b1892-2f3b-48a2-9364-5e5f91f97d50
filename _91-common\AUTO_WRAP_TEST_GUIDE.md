# 自动封装功能测试指南

## 📋 **功能概述**

全局响应处理器会自动将Controller返回的非ApiResult类型数据封装为统一的ApiResult格式，提供一致的API响应结构。

## 🚀 **测试步骤**

### 1. 启动测试项目

```bash
cd _05-using-common
mvn spring-boot:run
```

项目将在 `http://localhost:8085` 启动。

### 2. 测试各种返回类型

#### 2.1 String类型自动封装

**请求**: `GET http://localhost:8085/auto-wrap/string`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": "这是一个字符串，应该被自动封装为ApiResult",
  "traceId": null,
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.2 Integer类型自动封装

**请求**: `GET http://localhost:8085/auto-wrap/integer`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": 12345,
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.3 Boolean类型自动封装

**请求**: `GET http://localhost:8085/auto-wrap/boolean`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": true,
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.4 void类型自动封装

**请求**: `POST http://localhost:8085/auto-wrap/void`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": null,
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.5 POJO对象自动封装

**请求**: `GET http://localhost:8085/auto-wrap/pojo`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "age": 25
  },
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.6 List集合自动封装

**请求**: `GET http://localhost:8085/auto-wrap/list`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "张三",
      "email": "<EMAIL>",
      "age": 25
    },
    {
      "id": 2,
      "name": "李四",
      "email": "<EMAIL>",
      "age": 30
    },
    {
      "id": 3,
      "name": "王五",
      "email": "<EMAIL>",
      "age": 28
    }
  ],
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.7 Map对象自动封装

**请求**: `GET http://localhost:8085/auto-wrap/map`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": {
    "message": "这是一个Map对象",
    "timestamp": 1704067200000,
    "success": true
  },
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

#### 2.8 手动ApiResult不重复封装

**请求**: `GET http://localhost:8085/auto-wrap/manual-result`

**期望响应**:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "这是手动封装的ApiResult",
  "data": {
    "id": 1,
    "name": "手动封装",
    "email": "<EMAIL>",
    "age": 35
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 3. 测试追踪ID功能

在请求头中添加 `X-Trace-Id`：

```bash
curl -H "X-Trace-Id: test-trace-123" http://localhost:8085/auto-wrap/string
```

**期望响应**包含追踪ID:
```json
{
  "success": true,
  "httpStatus": 200,
  "code": "1000",
  "message": "操作成功",
  "data": "这是一个字符串，应该被自动封装为ApiResult",
  "traceId": "test-trace-123",
  "timestamp": "2024-01-01 12:00:00",
  "server": "using-common-server",
  "version": "v1.0"
}
```

## 🔧 **配置测试**

### 测试禁用String类型自动封装

修改 `application.yml`:
```yaml
project91:
  global-response:
    handle-string-response: false
```

重启应用后，访问 `GET http://localhost:8085/auto-wrap/string` 应该直接返回字符串而不是ApiResult格式。

### 测试禁用全局响应处理器

修改 `application.yml`:
```yaml
project91:
  global-response:
    enabled: false
```

重启应用后，所有接口都应该返回原始格式，不进行自动封装。

## ✅ **验证要点**

1. **自动封装**: 非ApiResult类型的返回值被自动包装
2. **保持原格式**: 已经是ApiResult类型的返回值不被重复包装
3. **类型支持**: 支持String、基本类型、POJO、集合、数组等
4. **配置控制**: 可以通过配置启用/禁用各种类型的自动封装
5. **追踪ID**: 自动从请求头提取并添加到响应中
6. **服务器信息**: 自动添加服务器名称和API版本信息
7. **时间戳**: 自动添加响应时间戳

## 🐛 **常见问题**

### 1. 自动封装不生效

- 检查配置 `project91.global-response.enabled` 是否为true
- 检查Controller类是否被排除在配置的排除列表中
- 检查返回值是否已经是ApiResult类型

### 2. 追踪ID不显示

- 检查请求头是否包含 `X-Trace-Id`
- 检查配置 `project91.global-response.auto-trace-id` 是否为true

### 3. 服务器信息不显示

- 检查配置 `project91.global-response.add-server-info` 是否为true
- 检查配置 `project91.global-response.server-name` 是否设置

这个测试指南帮助验证自动封装功能是否按预期工作。
