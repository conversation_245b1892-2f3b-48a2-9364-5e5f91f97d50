spring:
  application:
    name: minio-service
  cloud:
    nacos:
      discovery:
        # 服务发现
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631  # 服务发现命名空间
      config:
        # 配置中心
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631 # 配置中心命名空间
        file-extension: yaml
        shared-configs:
          - data-id: other-config.yaml
            group: OTHER_GROUP
            refresh: true