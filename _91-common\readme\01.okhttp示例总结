// 1. 简单GET请求
HttpResponse response = httpClient.get("https://api.example.com/users").execute();

// 2. 带参数的GET请求
HttpResponse response = httpClient.get("https://api.example.com/users")
    .param("page", 1)
    .param("size", 10)
    .execute();

// 3. POST JSON请求
User user = new User("张三", "<EMAIL>");
HttpResponse response = httpClient.post("https://api.example.com/users")
    .json(user)
    .execute();

// 4. 异步请求
CompletableFuture<HttpResponse> future = httpClient.get("https://api.example.com/users")
    .executeAsync();

// 5. 表单提交
Map<String, String> form = Map.of("name", "张三", "email", "<EMAIL>");
HttpResponse response = httpClient.post("https://api.example.com/users")
    .form(form)
    .execute();


这个框架具有以下特点：

链式调用：API设计简洁易用
自动重试：网络异常时自动重试
日志记录：详细的请求响应日志
异步支持：支持同步和异步请求
类型转换：自动JSON序列化/反序列化
配置灵活：支持配置文件自定义
异常处理：统一的异常处理机制