package com.example._06minio.controller;

import com.example._06minio.common.Result;
import com.example._06minio.config.NacosConfig;
import com.example._06minio.config.OtherGroupConfig;
import com.example._06minio.service.MinioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class TestController {

    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${server.port}")
    private String port;
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello from " + applicationName + " on port " + port;
    }



    @Autowired
    private NacosConfig nacosConfig;

    @Autowired
    private OtherGroupConfig otherGroupConfig;

    @Autowired
    private MinioService minioService;

//    @Resource
//    private OkHttpUtil


    @GetMapping("/get")
    public String getConfig() {

        return "从Nacos获取的配置信息: " + nacosConfig.getConfigInfo();
    }

    @GetMapping("/get-other")
    public String getOtherGroupConfig() {
        return "从Nacos OTHER_GROUP组获取的配置信息: " + otherGroupConfig.getCustomProperty();
    }

    @GetMapping("/test")
    public String test() {
        return "MinIO Service is running!";
    }

    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        try {
            List<String> buckets = minioService.getAllBuckets();
            healthInfo.put("status", "UP");
            healthInfo.put("minio", "Connected");
            healthInfo.put("buckets", buckets);
        } catch (Exception e) {
            healthInfo.put("status", "DOWN");
            healthInfo.put("minio", "Disconnected");
            healthInfo.put("error", e.getMessage());
        }
        return Result.success(healthInfo);
    }
}