# MinIO 文件存储服务

这是一个基于Spring Boot和MinIO的文件存储服务，提供文件上传、下载、删除等功能。

## 功能特性

- 文件上传/下载
- 存储桶管理
- 文件列表查看
- 预签名URL生成
- 文件信息查询
- 支持多种文件类型
- RESTful API接口
- Web测试界面

## 环境要求

- JDK 17+
- Maven 3.6+
- MinIO Server

## 快速开始

### 1. 启动MinIO服务器

```bash
# 使用Docker启动MinIO
docker run -p 9000:9000 -p 9001:9001 \
  --name minio \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  minio/minio server /data --console-address ":9001"
```

访问MinIO控制台: http://localhost:9001
用户名: minioadmin
密码: minioadmin

### 2. 配置应用

修改 `application.yml` 中的MinIO配置：

```yaml
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: test-bucket
```

### 3. 启动应用

```bash
mvn spring-boot:run
```

应用将在 http://localhost:8086 启动

### 4. 测试服务

访问测试页面: http://localhost:8086/index.html

## API接口

### 存储桶管理

- `POST /api/minio/bucket/{bucketName}` - 创建存储桶
- `GET /api/minio/bucket/{bucketName}/exists` - 检查存储桶是否存在
- `GET /api/minio/buckets` - 获取所有存储桶
- `DELETE /api/minio/bucket/{bucketName}` - 删除存储桶

### 文件操作

- `POST /api/minio/upload` - 上传文件到默认存储桶
- `POST /api/minio/upload/{bucketName}` - 上传文件到指定存储桶
- `GET /api/minio/download/{objectName}` - 从默认存储桶下载文件
- `GET /api/minio/download/{bucketName}/{objectName}` - 从指定存储桶下载文件
- `DELETE /api/minio/file/{objectName}` - 从默认存储桶删除文件
- `DELETE /api/minio/file/{bucketName}/{objectName}` - 从指定存储桶删除文件

### 文件信息

- `GET /api/minio/files` - 列出默认存储桶中的所有文件
- `GET /api/minio/files/{bucketName}` - 列出指定存储桶中的所有文件
- `GET /api/minio/file-info/{bucketName}/{objectName}` - 获取文件信息
- `GET /api/minio/presigned-url/{bucketName}/{objectName}` - 获取预签名URL

### 健康检查

- `GET /health` - 服务健康检查
- `GET /test` - 简单测试接口

## 使用示例

### 上传文件

```bash
curl -X POST -F "file=@test.txt" http://localhost:8086/api/minio/upload
```

### 下载文件

```bash
curl -O http://localhost:8086/api/minio/download/{objectName}
```

### 获取文件列表

```bash
curl http://localhost:8086/api/minio/files
```

## 作为依赖使用

其他项目可以通过以下方式调用此服务：

### 1. 通过HTTP客户端调用

```java
@Service
public class FileService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public String uploadFile(MultipartFile file) {
        String url = "http://minio-service:8086/api/minio/upload";
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = 
            new HttpEntity<>(body, headers);
            
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        return response.getBody();
    }
}
```

### 2. 通过Feign客户端调用

```java
@FeignClient(name = "minio-service")
public interface MinioFeignClient {
    
    @PostMapping(value = "/api/minio/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<FileInfo> uploadFile(@RequestPart("file") MultipartFile file);
    
    @GetMapping("/api/minio/files")
    Result<List<FileInfo>> listFiles();
    
    @DeleteMapping("/api/minio/file/{objectName}")
    Result<Void> deleteFile(@PathVariable String objectName);
}
```

## 注意事项

1. 确保MinIO服务器正在运行
2. 检查网络连接和防火墙设置
3. 文件上传大小限制为100MB（可在配置中修改）
4. 预签名URL默认有效期为7天

## 故障排除

1. **连接MinIO失败**: 检查MinIO服务器是否启动，端口是否正确
2. **文件上传失败**: 检查文件大小限制和存储桶权限
3. **服务发现问题**: 确保Nacos服务器正在运行
