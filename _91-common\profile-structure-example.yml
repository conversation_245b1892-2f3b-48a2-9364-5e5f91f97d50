# ==================== 文档段落 1 ====================
# 默认配置（没有 profile 标记，所有环境都会加载）
server:
  port: 8080

okhttp:
  enabled: true
  connect-timeout: 10s

logging:
  level:
    root: INFO

# 激活 dev profile
spring:
  profiles:
    active: dev

# ==================== 文档段落 2 ====================
---
# dev profile 配置
spring:
  profiles: dev

server:
  port: 8081  # 覆盖默认的 8080

okhttp:
  connect-timeout: 30s  # 覆盖默认的 10s
  read-timeout: 60s     # 新增配置
  logging:
    enabled: true
    level: DEBUG

logging:
  level:
    root: DEBUG  # 覆盖默认的 INFO

# ==================== 文档段落 3 ====================
---
# test profile 配置
spring:
  profiles: test

server:
  port: 8082

okhttp:
  connect-timeout: 5s
  read-timeout: 10s
  logging:
    enabled: true
    level: BASIC

logging:
  level:
    root: DEBUG

# ==================== 文档段落 4 ====================
---
# prod profile 配置
spring:
  profiles: prod

server:
  port: 8083

okhttp:
  connect-timeout: 3s
  read-timeout: 15s
  logging:
    enabled: false

logging:
  level:
    root: WARN
