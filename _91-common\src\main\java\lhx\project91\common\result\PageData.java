package lhx.project91.common.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页数据封装类
 * 
 * <p>用于封装分页查询的结果数据，包含：</p>
 * <ul>
 *   <li>当前页数据列表</li>
 *   <li>分页信息（页码、页大小、总数等）</li>
 *   <li>便捷的分页计算方法</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>{@code
 * // 创建分页数据
 * List<User> users = userService.findUsers(pageNum, pageSize);
 * long total = userService.countUsers();
 * 
 * PageData<User> pageData = PageData.<User>builder()
 *     .records(users)
 *     .current(pageNum)
 *     .size(pageSize)
 *     .total(total)
 *     .build();
 * 
 * // 返回分页结果
 * return ApiResult.success(pageData);
 * }</pre>
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageData<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页数据列表
     */
    @Builder.Default
    private List<T> records = Collections.emptyList();

    /**
     * 当前页码（从1开始）
     */
    @Builder.Default
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Builder.Default
    private Long size = 10L;

    /**
     * 总记录数
     */
    @Builder.Default
    private Long total = 0L;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    /**
     * 当前页记录数
     */
    private Integer currentSize;

    // ==================== 静态工厂方法 ====================

    /**
     * 创建空的分页数据
     * 
     * @param <T> 数据类型
     * @return 空的分页数据
     */
    public static <T> PageData<T> empty() {
        return PageData.<T>builder().build().calculatePageInfo();
    }

    /**
     * 创建空的分页数据（指定页码和页大小）
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param <T> 数据类型
     * @return 空的分页数据
     */
    public static <T> PageData<T> empty(Long current, Long size) {
        return PageData.<T>builder()
                .current(current)
                .size(size)
                .build()
                .calculatePageInfo();
    }

    /**
     * 创建分页数据
     * 
     * @param records 数据列表
     * @param current 当前页码
     * @param size 每页大小
     * @param total 总记录数
     * @param <T> 数据类型
     * @return 分页数据
     */
    public static <T> PageData<T> of(List<T> records, Long current, Long size, Long total) {
        return PageData.<T>builder()
                .records(records != null ? records : Collections.emptyList())
                .current(current)
                .size(size)
                .total(total)
                .build()
                .calculatePageInfo();
    }

    /**
     * 从现有分页数据转换为新类型的分页数据
     * 
     * @param sourcePageData 源分页数据
     * @param targetRecords 目标数据列表
     * @param <S> 源数据类型
     * @param <T> 目标数据类型
     * @return 新类型的分页数据
     */
    public static <S, T> PageData<T> convert(PageData<S> sourcePageData, List<T> targetRecords) {
        return PageData.<T>builder()
                .records(targetRecords != null ? targetRecords : Collections.emptyList())
                .current(sourcePageData.getCurrent())
                .size(sourcePageData.getSize())
                .total(sourcePageData.getTotal())
                .build()
                .calculatePageInfo();
    }

    // ==================== 计算方法 ====================

    /**
     * 计算并设置分页信息
     * 
     * @return 当前PageData实例（支持链式调用）
     */
    public PageData<T> calculatePageInfo() {
        // 确保基本参数不为null
        if (this.current == null || this.current < 1) {
            this.current = 1L;
        }
        if (this.size == null || this.size < 1) {
            this.size = 10L;
        }
        if (this.total == null) {
            this.total = 0L;
        }
        if (this.records == null) {
            this.records = Collections.emptyList();
        }

        // 计算总页数
        this.pages = this.total == 0 ? 0 : (this.total + this.size - 1) / this.size;

        // 计算分页状态
        this.hasPrevious = this.current > 1;
        this.hasNext = this.current < this.pages;
        this.isFirst = this.current == 1;
        this.isLast = this.current.equals(this.pages) || this.pages == 0;

        // 当前页记录数
        this.currentSize = this.records.size();

        return this;
    }

    /**
     * 获取起始记录索引（从0开始）
     * 
     * @return 起始记录索引
     */
    public long getStartIndex() {
        return (this.current - 1) * this.size;
    }

    /**
     * 获取结束记录索引（从0开始，不包含）
     * 
     * @return 结束记录索引
     */
    public long getEndIndex() {
        return Math.min(this.getStartIndex() + this.size, this.total);
    }

    /**
     * 判断是否为空页面
     * 
     * @return true表示空页面
     */
    public boolean isEmpty() {
        return this.records == null || this.records.isEmpty();
    }

    /**
     * 判断是否有数据
     * 
     * @return true表示有数据
     */
    public boolean hasContent() {
        return !isEmpty();
    }

    /**
     * 获取上一页页码
     * 
     * @return 上一页页码，如果没有上一页则返回null
     */
    public Long getPreviousPage() {
        return this.hasPrevious ? this.current - 1 : null;
    }

    /**
     * 获取下一页页码
     * 
     * @return 下一页页码，如果没有下一页则返回null
     */
    public Long getNextPage() {
        return this.hasNext ? this.current + 1 : null;
    }

    // ==================== 便捷方法 ====================

    /**
     * 设置数据列表并重新计算分页信息
     * 
     * @param records 数据列表
     * @return 当前PageData实例
     */
    public PageData<T> setRecordsAndCalculate(List<T> records) {
        this.records = records != null ? records : Collections.emptyList();
        return this.calculatePageInfo();
    }

    /**
     * 设置总数并重新计算分页信息
     * 
     * @param total 总记录数
     * @return 当前PageData实例
     */
    public PageData<T> setTotalAndCalculate(Long total) {
        this.total = total != null ? total : 0L;
        return this.calculatePageInfo();
    }

    /**
     * 转换为简单的分页信息字符串
     * 
     * @return 分页信息字符串
     */
    public String getPageInfo() {
        return String.format("第%d页/共%d页，每页%d条，共%d条记录", 
                this.current, this.pages, this.size, this.total);
    }
}
