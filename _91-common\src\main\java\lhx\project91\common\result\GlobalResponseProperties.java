package lhx.project91.common.result;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 全局响应处理器配置属性
 * 
 * <p>用于配置全局响应处理器和异常处理器的行为</p>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "project91.global-response")
public class GlobalResponseProperties {

    /**
     * 是否启用全局响应处理器
     */
    private boolean enabled = true;

    /**
     * 是否启用全局异常处理器
     */
    private boolean exceptionHandlerEnabled = true;

    /**
     * 排除的包路径列表
     * 这些包下的Controller不会被全局响应处理器处理
     */
    private List<String> excludePackages = new ArrayList<>();

    /**
     * 排除的类名模式列表
     * 匹配这些模式的类不会被全局响应处理器处理
     */
    private List<String> excludeClassPatterns = new ArrayList<>();

    /**
     * 是否处理String类型的返回值
     */
    private boolean handleStringResponse = true;

    /**
     * 是否处理void类型的返回值
     */
    private boolean handleVoidResponse = true;

    /**
     * 是否处理基本数据类型的返回值
     */
    private boolean handlePrimitiveResponse = true;

    /**
     * 是否处理集合类型的返回值
     */
    private boolean handleCollectionResponse = true;

    /**
     * 是否自动添加追踪ID
     */
    private boolean autoTraceId = true;

    /**
     * 追踪ID的请求头名称
     */
    private String traceIdHeader = "X-Trace-Id";

    /**
     * 是否添加服务器信息
     */
    private boolean addServerInfo = false;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * API版本
     */
    private String apiVersion;

    /**
     * 是否在开发环境下显示详细错误信息
     */
    private boolean showDetailErrorInDev = true;

    /**
     * 开发环境的profile名称
     */
    private List<String> devProfiles = List.of("dev", "development", "local");

    /**
     * 判断是否应该排除指定的类
     * 
     * @param className 类名
     * @return true表示应该排除
     */
    public boolean shouldExcludeClass(String className) {
        // 检查包路径排除
        for (String excludePackage : excludePackages) {
            if (className.startsWith(excludePackage)) {
                return true;
            }
        }
        
        // 检查类名模式排除
        for (String pattern : excludeClassPatterns) {
            if (className.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取默认配置
     * 
     * @return 默认配置实例
     */
    public static GlobalResponseProperties defaultConfig() {
        GlobalResponseProperties config = new GlobalResponseProperties();
        
        // 默认排除的包
        config.excludePackages.add("springfox.documentation");
        config.excludePackages.add("org.springframework.boot.actuate");
        config.excludePackages.add("org.springframework.boot.autoconfigure.web.servlet.error");
        
        // 默认排除的类名模式
        config.excludeClassPatterns.add("ErrorController");
        config.excludeClassPatterns.add("BasicErrorController");
        config.excludeClassPatterns.add("SwaggerController");
        
        return config;
    }
}
