1.加入依赖
 - 1.gatway依赖
 - 2.nacos服务发现
 - 3.bootstrap
 - 4.负载均衡（使用这个配合上 bootstrap.yaml中的配置,就可以直接通过 服务名去调用到服务）

2.bootstrap进行配置
server:
  port: 88
spring:
  application:
    name: nacos-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true # 开启服务发现自动路由
    nacos:
      discovery:
        # 服务发现
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631  # 服务发现命名空间
        enabled: true #开启拉取功能
        register-enabled: false #关闭注册功能 (✅ 可以调用别人（拉取服务列表）  &&&  ❌ 别人调不到它（注册中心里没有它）)

3.过滤器去实现
只要去实现implements GlobalFilter，然后实现对应的方法
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        System.out.println("===================filter 我进来了===================");
        // 继续执行过滤器链
        return chain.filter(exchange);
    }