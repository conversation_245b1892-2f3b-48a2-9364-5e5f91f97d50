package com.example._05usingcommon.okhttp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * HTTP请求结果封装类
 * 临时替代91项目的HttpResult
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HttpResult<T> {
    
    /**
     * 响应状态码
     */
    private int statusCode;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 请求耗时（毫秒）
     */
    private long duration;
    
    /**
     * 是否成功
     */
    private boolean successful;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 响应头信息
     */
    private String headers;
    
    /**
     * 创建成功结果
     */
    public static <T> HttpResult<T> success(T data, int statusCode, long duration) {
        return HttpResult.<T>builder()
                .data(data)
                .statusCode(statusCode)
                .duration(duration)
                .successful(true)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static <T> HttpResult<T> failure(int statusCode, String errorMessage, long duration) {
        return HttpResult.<T>builder()
                .statusCode(statusCode)
                .errorMessage(errorMessage)
                .duration(duration)
                .successful(false)
                .build();
    }
    
    /**
     * 判断是否为成功的HTTP状态码
     */
    public boolean isSuccessful() {
        return successful && statusCode >= 200 && statusCode < 300;
    }
}
