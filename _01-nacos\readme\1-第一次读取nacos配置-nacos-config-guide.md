# Nacos配置中心使用指南

## 在Nacos中添加配置
1. 登录Nacos控制台（默认地址：http://127.0.0.1:8848/nacos，默认账号密码：nacos/nacos）
2. 点击左侧菜单栏中的"配置管理" -> "配置列表"
3. 点击右上角的"+"按钮，添加配置
4. 填写配置信息：
   - Data ID: `nacos-demo.yaml` (格式为`${spring.application.name}.${file-extension}`，根据bootstrap.yml中的配置)
   - Group: 默认为`DEFAULT_GROUP`，可以保持默认
   - 配置格式：选择`YAML`
   - 配置内容：
     ```yaml
     config:
       info: "这是从Nacos配置中心加载的配置"
     ```
5. 点击"发布"按钮保存配置

## 测试配置读取
1. 启动应用程序
2. 访问：`http://localhost:8080/config/get`
3. 应该能看到从Nacos获取的配置信息
4. 在Nacos控制台修改配置值，几秒钟后再次访问接口，可以观察到配置已经动态刷新

## 注意事项
- 确保Nacos服务已经启动
- 确保bootstrap.yml中的Nacos地址配置正确
- 配置的DataID必须符合`${spring.application.name}.${file-extension}`的格式
- `@RefreshScope`注解用于实现配置的动态刷新，修改Nacos中的配置后会自动更新 