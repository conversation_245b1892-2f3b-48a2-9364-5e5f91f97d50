package com.example._05usingcommon.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自动封装配置类
 * 启用全局响应处理器
 */
@Configuration
public class AutoWrapConfiguration {

    /**
     * 注册全局响应处理器
     */
    @Bean
    public GlobalResponseConfig.SimpleGlobalResponseHandler simpleGlobalResponseHandler() {
        return new GlobalResponseConfig.SimpleGlobalResponseHandler();
    }
}
