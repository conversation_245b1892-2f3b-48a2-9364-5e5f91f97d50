<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MinIO 文件服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .file-list {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>MinIO 文件服务测试页面</h1>
    
    <div class="container">
        <h3>服务状态检查</h3>
        <button onclick="checkHealth()">检查服务状态</button>
        <div id="healthResult" class="result"></div>
    </div>

    <div class="container">
        <h3>文件上传</h3>
        <div class="upload-area">
            <input type="file" id="fileInput" multiple>
            <br><br>
            <button onclick="uploadFile()">上传文件</button>
        </div>
        <div id="uploadResult" class="result"></div>
    </div>

    <div class="container">
        <h3>文件列表</h3>
        <button onclick="listFiles()">刷新文件列表</button>
        <div id="fileList" class="result file-list"></div>
    </div>

    <div class="container">
        <h3>存储桶管理</h3>
        <input type="text" id="bucketName" placeholder="输入存储桶名称">
        <button onclick="createBucket()">创建存储桶</button>
        <button onclick="listBuckets()">列出所有存储桶</button>
        <div id="bucketResult" class="result"></div>
    </div>

    <script>
        const baseUrl = '/api/minio';

        async function checkHealth() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                document.getElementById('healthResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('healthResult').textContent = '错误: ' + error.message;
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (files.length === 0) {
                alert('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', files[0]);

            try {
                const response = await fetch(baseUrl + '/upload', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                document.getElementById('uploadResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('uploadResult').textContent = '错误: ' + error.message;
            }
        }

        async function listFiles() {
            try {
                const response = await fetch(baseUrl + '/files');
                const data = await response.json();
                document.getElementById('fileList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('fileList').textContent = '错误: ' + error.message;
            }
        }

        async function createBucket() {
            const bucketName = document.getElementById('bucketName').value;
            if (!bucketName) {
                alert('请输入存储桶名称');
                return;
            }

            try {
                const response = await fetch(baseUrl + '/bucket/' + bucketName, {
                    method: 'POST'
                });
                const data = await response.json();
                document.getElementById('bucketResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('bucketResult').textContent = '错误: ' + error.message;
            }
        }

        async function listBuckets() {
            try {
                const response = await fetch(baseUrl + '/buckets');
                const data = await response.json();
                document.getElementById('bucketResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('bucketResult').textContent = '错误: ' + error.message;
            }
        }

        // 页面加载时检查服务状态
        window.onload = function() {
            checkHealth();
        };
    </script>
</body>
</html>
