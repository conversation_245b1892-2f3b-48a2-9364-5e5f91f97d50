package com.example._04okhttp.controller;

import com.example._04okhttp.http.HttpClient;
import com.example._04okhttp.http.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * HTTP客户端使用示例
 */
@RestController
@RequestMapping("/demo")
public class HttpDemoController {
    
    @Autowired
    private HttpClient httpClient;
    
    /**
     * GET请求示例
     */
    @GetMapping("/get-example")
    public Map<String, Object> getExample() {
        HttpResponse response = httpClient
            .get("https://jsonplaceholder.typicode.com/posts/1")
            .header("User-Agent", "MyApp/1.0")
            .execute();
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", response.code());
        result.put("data", response.asMap());
        
        response.close();
        return result;
    }
    
    /**
     * POST请求示例
     */
    @PostMapping("/post-example")
    public Map<String, Object> postExample(@RequestBody Map<String, Object> data) {
        Map<String, Object> postData = new HashMap<>();
        postData.put("title", "测试标题");
        postData.put("body", "测试内容");
        postData.put("userId", 1);
        
        HttpResponse response = httpClient
            .post("https://jsonplaceholder.typicode.com/posts")
            .header("Content-Type", "application/json")
            .json(postData)
            .execute();
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", response.code());
        result.put("data", response.asMap());
        
        response.close();
        return result;
    }
    
    /**
     * 异步请求示例
     */
//    ✅ 解释：
//    在 Spring Web（尤其是支持异步响应的框架，如 Spring WebFlux 或 Servlet 3.1+）中：
//    当你返回一个 CompletableFuture（或 Mono、DeferredResult 等），Spring 会立刻释放处理请求的线程。
//    请求本身会被标记为“异步处理中”，不会立即给客户端返回响应。
//    当 CompletableFuture 完成后，Spring 会重新拿一个线程，把结果写回客户端。
//    ❗注意：
//    客户端不会立刻收到响应，而是等到 CompletableFuture 完成后才收到。
//    但服务器端的 Controller 方法本身已经执行完并返回了，不会阻塞。
    @GetMapping("/async-example")
    public CompletableFuture<Map<String, Object>> asyncExample() {
//        return httpClient
//            .get("https://jsonplaceholder.typicode.com/posts")
//            .param("_limit", 5)
//            .executeAsync()
//            .thenApply(response -> {
//                Map<String, Object> result = new HashMap<>();
//                result.put("status", response.code());
//                result.put("data", response.string());
//                response.close();
//                return result;
//            });


        CompletableFuture<Map<String, Object>> future = httpClient
                .get("https://jsonplaceholder.typicode.com/posts")
                .param("_limit", 5)
                .executeAsync()
                .thenApply(response -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("status", response.code());
                    result.put("data", response.string());
                    response.close();
                    return result;
                });
        // ✅ 这行会立即执行，不会等请求完成
        System.out.println("请求已发出，我先走了");

        return future; // 请求结果稍后会异步返回
    }
    
    /**
     * 表单提交示例
     */
    @PostMapping("/form-example")
    public Map<String, Object> formExample() {
        Map<String, String> formData = new HashMap<>();
        formData.put("name", "张三");
        formData.put("email", "<EMAIL>");
        
        HttpResponse response = httpClient
            .post("https://httpbin.org/post")
            .form(formData)
            .execute();
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", response.code());
        result.put("data", response.asMap());
        
        response.close();
        return result;
    }
    
    /**
     * 带参数的GET请求示例
     */
    @GetMapping("/params-example")
    public Map<String, Object> paramsExample() {
        HttpResponse response = httpClient
            .get("https://jsonplaceholder.typicode.com/posts")
            .param("userId", 1)
            .param("_limit", 3)
            .header("Accept", "application/json")
            .execute();
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", response.code());
        result.put("data", response.string());
        
        response.close();
        return result;
    }
}