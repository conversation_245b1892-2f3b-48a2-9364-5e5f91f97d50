package lhx.project91.okhttp.exception;

/**
 * OkHttp 异常类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
public class OkHttpException extends RuntimeException {

    private final int statusCode;
    private final String url;
    private final String method;

    public OkHttpException(String message) {
        super(message);
        this.statusCode = -1;
        this.url = null;
        this.method = null;
    }

    public OkHttpException(String message, Throwable cause) {
        super(message, cause);
        this.statusCode = -1;
        this.url = null;
        this.method = null;
    }

    public OkHttpException(int statusCode, String message) {
        super(message);
        this.statusCode = statusCode;
        this.url = null;
        this.method = null;
    }

    public OkHttpException(int statusCode, String message, String url, String method) {
        super(message);
        this.statusCode = statusCode;
        this.url = url;
        this.method = method;
    }

    public OkHttpException(int statusCode, String message, String url, String method, Throwable cause) {
        super(message, cause);
        this.statusCode = statusCode;
        this.url = url;
        this.method = method;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getUrl() {
        return url;
    }

    public String getMethod() {
        return method;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OkHttpException{");
        if (statusCode > 0) {
            sb.append("statusCode=").append(statusCode);
        }
        if (url != null) {
            sb.append(", url='").append(url).append('\'');
        }
        if (method != null) {
            sb.append(", method='").append(method).append('\'');
        }
        sb.append(", message='").append(getMessage()).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
