package com.example._03auth.security;

import com.example._03auth.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器
 * 
 * 继承OncePerRequestFilter确保每个请求只执行一次
 * 主要功能：
 * 1. 从请求头提取JWT token
 * 2. 验证token的有效性
 * 3. 解析token中的用户信息和权限
 * 4. 设置Spring Security的认证上下文
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    /**
     * JWT工具类，用于token的解析和验证
     */
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 过滤器核心方法，处理每个HTTP请求
     * 
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param filterChain 过滤器链，用于继续执行后续过滤器
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // 1. 从请求头获取Authorization字段
        // 格式应该是: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        String authHeader = request.getHeader("Authorization");
        
        // 2. 检查Authorization头是否存在且格式正确
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // 3. 提取JWT token（去掉"Bearer "前缀）
            String token = authHeader.substring(7);
            
            // 4. 验证token的有效性（签名、过期时间等）
            if (jwtUtil.validateToken(token)) {
                // 5. 从token中提取用户名
                String username = jwtUtil.getUsernameFromToken(token);
                
                // 6. 从token中提取用户角色列表
                List<String> roles = jwtUtil.getRolesFromToken(token);
                
                // 7. 将角色字符串转换为Spring Security的权限对象
                List<SimpleGrantedAuthority> authorities = roles.stream()
                    .map(SimpleGrantedAuthority::new)  // 创建权限对象
                    .collect(Collectors.toList());
                
                // 8. 创建Spring Security的认证对象
                // 参数：principal(用户名), credentials(密码，JWT认证时为null), authorities(权限列表)
                UsernamePasswordAuthenticationToken authentication = 
                    new UsernamePasswordAuthenticationToken(username, null, authorities);
                
                // 9. 将认证对象设置到Spring Security上下文中
                // 这样后续的过滤器和Controller就知道当前用户已认证
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
            // 如果token无效，不设置认证信息，请求会被后续的安全过滤器拦截
        }
        
        // 10. 继续执行过滤器链中的下一个过滤器
        // 无论认证是否成功都要继续，让Spring Security的其他过滤器处理
        filterChain.doFilter(request, response);
    }
}
