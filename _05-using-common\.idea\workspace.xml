<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1f579bf6-006d-49ec-bd3c-4326bfb21b4d" name="更改" comment="05_using-common - 第二次 - 引用91项目的依赖 - 成功使用okhttputil工具">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/_05usingcommon/controller/TestController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/_05usingcommon/controller/TestController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="01_nacos" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="05_using-common" />
                    <option name="lastUsedInstant" value="1753423989" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="01_nacos" />
                    <option name="lastUsedInstant" value="1752654663" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1752654662" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GradleLocalSettings">
    <option name="myGradleUserHome" value="$PROJECT_DIR$/../../../../soft/maven-repo" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\soft\Maven\apache-maven-3.9.8-bin-nexus\apache-maven-3.9.8" />
        <option name="localRepository" value="D:\soft\maven-repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\soft\Maven\apache-maven-3.9.8-bin-nexus\apache-maven-3.9.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="downloadAnnotationsAutomatically" value="true" />
        <option name="downloadDocsAutomatically" value="true" />
        <option name="downloadSourcesAutomatically" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zADsuPUS0surLbBf23guepiiaa" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.helloworld [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.helloworld [org.apache.maven.plugins:maven-clean-plugin:3.2.0:help].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.Application.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;05__using-common&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/JavaProject1/mypro/study-nacos/_05-using-common&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settings.project.maven.importing&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.Application.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\JavaProject1\mypro\study-nacos\_01-nacos\readme" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\JavaProject1\mypro\study-nacos\_01-nacos\readme" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.Application">
    <configuration name="Application" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example._05usingcommon.Application" />
      <module name="_01-nacos" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example._05usingcommon.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="helloworld" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example._05usingcommon.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="1f579bf6-006d-49ec-bd3c-4326bfb21b4d" name="更改" comment="" />
      <created>1751165194589</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751165194589</updated>
      <workItem from="1751165195452" duration="5000" />
      <workItem from="1751180423282" duration="836000" />
      <workItem from="1751204120253" duration="7934000" />
      <workItem from="1751244744053" duration="14000" />
      <workItem from="1751785667893" duration="12690000" />
      <workItem from="1752547245079" duration="5155000" />
      <workItem from="1752627932337" duration="756000" />
      <workItem from="1752637452178" duration="1246000" />
      <workItem from="1752648087623" duration="8307000" />
      <workItem from="1752714079846" duration="2861000" />
      <workItem from="1752741193362" duration="15000" />
      <workItem from="1753022513418" duration="1901000" />
      <workItem from="1753060535854" duration="2548000" />
      <workItem from="1753105733885" duration="5000" />
      <workItem from="1753106160410" duration="1104000" />
      <workItem from="1753144373937" duration="760000" />
      <workItem from="1753164796273" duration="286000" />
      <workItem from="1753165098183" duration="4000" />
      <workItem from="1753274199906" duration="1058000" />
      <workItem from="1753275267300" duration="39000" />
      <workItem from="1753275317542" duration="348000" />
      <workItem from="1753275690302" duration="2306000" />
      <workItem from="1753338615765" duration="2593000" />
      <workItem from="1753341238668" duration="189000" />
      <workItem from="1753341457149" duration="3970000" />
      <workItem from="1753358323637" duration="479000" />
      <workItem from="1753359300011" duration="2611000" />
      <workItem from="1753362250793" duration="856000" />
      <workItem from="1753363120022" duration="2424000" />
      <workItem from="1753372587865" duration="2334000" />
      <workItem from="1753374940395" duration="39000" />
      <workItem from="1753374990083" duration="93000" />
      <workItem from="1753375090223" duration="674000" />
      <workItem from="1753406761640" duration="1341000" />
      <workItem from="1753408835869" duration="4511000" />
      <workItem from="1753415526796" duration="808000" />
      <workItem from="1753416379146" duration="75000" />
      <workItem from="1753416466384" duration="731000" />
      <workItem from="1753419822528" duration="603000" />
      <workItem from="1753420677840" duration="3719000" />
      <workItem from="1753685096451" duration="57000" />
      <workItem from="1753685169789" duration="3389000" />
      <workItem from="1753694733914" duration="1545000" />
      <workItem from="1753751221836" duration="1353000" />
    </task>
    <task id="LOCAL-00001" summary="ok-注册服务-注册到nacos的服务列表上">
      <option name="closed" value="true" />
      <created>1751789243806</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751789243806</updated>
    </task>
    <task id="LOCAL-00002" summary="随意配置一下mysql">
      <option name="closed" value="true" />
      <created>1751790362227</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751790362227</updated>
    </task>
    <task id="LOCAL-00003" summary="随意配置一下mysql">
      <option name="closed" value="true" />
      <created>1751790806905</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751790806905</updated>
    </task>
    <task id="LOCAL-00004" summary="ok-读取-其他组上的nacos配置">
      <option name="closed" value="true" />
      <created>1751792350239</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751792350239</updated>
    </task>
    <task id="LOCAL-00005" summary="ok-切换为新的命名空间 （目前就只能使用一种命名空间）&#10;&#10;---&#10;如果想要同时用两种，需要在代码中配置，比较麻烦">
      <option name="closed" value="true" />
      <created>1751809514601</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751809514601</updated>
    </task>
    <task id="LOCAL-00006" summary="注释">
      <option name="closed" value="true" />
      <created>1752649170814</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752649170814</updated>
    </task>
    <task id="LOCAL-00007" summary="OK - 配置全放在 nacos上去">
      <option name="closed" value="true" />
      <created>1752652339927</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752652339927</updated>
    </task>
    <task id="LOCAL-00008" summary="05_using-common - 引用91项目的依赖 - 成功使用okhttputil工具">
      <option name="closed" value="true" />
      <created>1753424161965</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753424161965</updated>
    </task>
    <task id="LOCAL-00009" summary="05_using-common - 第二次 - 引用91项目的依赖 - 成功使用okhttputil工具">
      <option name="closed" value="true" />
      <created>1753689962645</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753689962645</updated>
    </task>
    <option name="localTasksCounter" value="10" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="ok-注册服务-注册到nacos的服务列表上" />
    <MESSAGE value="ok-读取nacos上的配置" />
    <MESSAGE value="ok-读取nacos上的配置" />
    <MESSAGE value="ok-读取-其他组上的nacos配置" />
    <MESSAGE value="ok-切换为新的命名空间 （目前就只能使用一种命名空间）&#10;&#10;---&#10;如果想要同时用两种，需要在代码中配置，比较麻烦" />
    <MESSAGE value="注释" />
    <MESSAGE value="OK - 配置全放在 nacos上去" />
    <MESSAGE value="05_using-common - 引用91项目的依赖 - 成功使用okhttputil工具" />
    <MESSAGE value="05_using-common - 第二次 - 引用91项目的依赖 - 成功使用okhttputil工具" />
    <option name="LAST_COMMIT_MESSAGE" value="05_using-common - 第二次 - 引用91项目的依赖 - 成功使用okhttputil工具" />
  </component>
</project>