package com.example._04okhttp.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.Response;
import java.util.List;
import java.util.Map;

/**
 * HTTP响应封装类
 * 提供便捷的响应数据获取方法
 */
public class HttpResponse {
    
    private final Response response;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private String bodyString;
    
    public HttpResponse(Response response) {
        this.response = response;
    }
    
    /**
     * 获取状态码
     */
    public int code() {
        return response.code();
    }
    
    /**
     * 判断请求是否成功
     */
    public boolean isSuccessful() {
        return response.isSuccessful();
    }
    
    /**
     * 获取响应头
     */
    public String header(String name) {
        return response.header(name);
    }
    
    /**
     * 获取所有响应头
     */
    public Map<String, List<String>> headers() {
        return response.headers().toMultimap();
    }
    
    /**
     * 获取响应体字符串
     */
    public String string() {
        if (bodyString == null) {
            try {
                bodyString = response.body().string();
            } catch (Exception e) {
                throw new HttpException("读取响应体失败", e);
            }
        }
        return bodyString;
    }
    
    /**
     * 将响应体转换为指定类型对象
     */
    public <T> T as(Class<T> clazz) {
        try {
            return objectMapper.readValue(string(), clazz);
        } catch (Exception e) {
            throw new HttpException("响应体转换失败", e);
        }
    }
    
    /**
     * 将响应体转换为Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> asMap() {
        return as(Map.class);
    }
    
    /**
     * 关闭响应
     */
    public void close() {
        if (response != null) {
            response.close();
        }
    }
}