package com.example._03auth.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component
public class JwtUtil {

    // 每次重启都会生成密钥的方式
//    private final SecretKey key = Keys.secretKeyFor(SignatureAlgorithm.HS256);

    // 使用固定的密钥字符串
    private final String SECRET = "mySecretKeyForJWTTokenGenerationAndValidation123456789";
    private final SecretKey key = Keys.hmacShaKeyFor(SECRET.getBytes());
    private final long expiration = 86400000; // 24小时

    // 新增：生成带角色的token
    public String generateToken(String username, List<String> roles) {
        return Jwts.builder()
                .setSubject(username)
                .claim("roles", roles)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(key)
                .compact();
    }
    
    // 保持兼容：原有方法
    public String generateToken(String username) {
        return generateToken(username, Arrays.asList("ROLE_USER"));
    }
    
    public String getUsernameFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getSubject();
    }
    
    // 新增：从token获取角色
    public List<String> getRolesFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
        
        List<String> roles = claims.get("roles", List.class);
        return roles != null ? roles : Arrays.asList("ROLE_USER");
    }
    
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }
}
