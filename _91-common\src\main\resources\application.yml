# OkHttp Spring Boot Starter 配置示例

okhttp:
  # 是否启用 OkHttp（默认: true）
  enabled: true
  
  # 连接超时时间（默认: 10s）
  connect-timeout: 10s
  
  # 读取超时时间（默认: 30s）
  read-timeout: 30s
  
  # 写入超时时间（默认: 30s）
  write-timeout: 30s
  
  # 调用超时时间（默认: 60s）
  call-timeout: 60s
  
  # 连接池配置
  connection-pool:
    # 最大空闲连接数（默认: 5）
    max-idle-connections: 5
    # 连接保持活跃时间（默认: 5m）
    keep-alive-duration: 5m
  
  # 重试配置
  retry:
    # 是否启用重试（默认: true）
    enabled: true
    # 最大重试次数（默认: 3）
    max-retries: 3
    # 重试间隔（默认: 1s）
    retry-interval: 1s
  
  # 日志配置
  logging:
    # 是否启用请求日志（默认: true）
    enabled: true
    # 日志级别：NONE, BASIC, HEADERS, BODY（默认: BASIC）
    level: BASIC

# 生产环境推荐配置
---
spring:
  profiles: prod

okhttp:
  connect-timeout: 5s
  read-timeout: 15s
  write-timeout: 15s
  call-timeout: 30s
  connection-pool:
    max-idle-connections: 10
    keep-alive-duration: 10m
  retry:
    enabled: true
    max-retries: 2
    retry-interval: 500ms
  logging:
    enabled: false
    level: NONE

# 开发环境推荐配置
---
spring:
  profiles: dev

okhttp:
  connect-timeout: 30s
  read-timeout: 60s
  write-timeout: 60s
  call-timeout: 120s
  connection-pool:
    max-idle-connections: 3
    keep-alive-duration: 3m
  retry:
    enabled: true
    max-retries: 5
    retry-interval: 2s
  logging:
    enabled: true
    level: BODY
