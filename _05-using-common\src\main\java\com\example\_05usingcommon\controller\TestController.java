package com.example._05usingcommon.controller;

import com.example._05usingcommon.config.NacosConfig;
import com.example._05usingcommon.config.OtherGroupConfig;
import com.example._05usingcommon.util.SimpleOkHttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class TestController {

    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${server.port}")
    private String port;
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello from " + applicationName + " on port " + port;
    }



    @Autowired
    private NacosConfig nacosConfig;

    @Autowired
    private OtherGroupConfig otherGroupConfig;

    @Autowired
    private SimpleOkHttpUtil simpleOkHttpUtil;

    /**
     * okhttp测试 - 调用外部API
     */
    @GetMapping("/okhttp")
    public String okhttp() {
        try {
            // 测试调用外部API
            return simpleOkHttpUtil.get("https://httpbin.org/get");
        } catch (Exception e) {
            return "OkHttp调用失败: " + e.getMessage();
        }
    }

    /**
     * okhttp测试 - 调用本地其他端口的服务
     */
    @GetMapping("/okhttp-local")
    public String okhttpLocal() {
        try {
            // 如果你有其他服务运行在8080端口，可以调用
            return simpleOkHttpUtil.get("http://localhost:8080/hello");
        } catch (Exception e) {
            return "OkHttp本地调用失败: " + e.getMessage();
        }
    }

    /**
     * okhttp测试 - 获取详细结果
     */
    @GetMapping("/okhttp-result")
    public String okhttpResult() {
        try {
            String result = simpleOkHttpUtil.get("https://httpbin.org/get");
            return "OkHttp调用成功，数据长度: " + result.length() + " 字符";
        } catch (Exception e) {
            return "OkHttp结果获取失败: " + e.getMessage();
        }
    }

    @Autowired
    private OkHttpTemplate okHttpTemplate;

    @GetMapping("/okhttp-template")
    public String okhttpTemplate() {
        try {
            HttpResult<String> result = okHttpTemplate.getForString("https://httpbin.org/get");
            if (result.isSuccessful()) {
                return "OkHttpTemplate成功: 状态码=" + result.getStatusCode() + ", 数据长度=" + result.getData().length();
            } else {
                return "OkHttpTemplate失败: " + result.getErrorMessage();
            }
        } catch (Exception e) {
            return "OkHttpTemplate异常: " + e.getMessage();
        }
    }


    @GetMapping("/get")
    public String getConfig() {

        return "从Nacos获取的配置信息: " + nacosConfig.getConfigInfo();
    }

    @GetMapping("/get-other")
    public String getOtherGroupConfig() {
        return "从Nacos OTHER_GROUP组获取的配置信息: " + otherGroupConfig.getCustomProperty();
    }

    /**
     * 简单的测试接口，用于其他服务调用
     */
    @GetMapping("/test-target")
    public String testTarget() {
        return "这是05项目的测试目标接口，当前时间: " + System.currentTimeMillis();
    }

    /**
     * OkHttp状态检查
     */
    @GetMapping("/okhttp-status")
    public String okhttpStatus() {
        return String.format("OkHttp工具类状态: %s, OkHttpTemplate状态: %s",
            okHttpUtil != null ? "已注入" : "未注入",
            okHttpTemplate != null ? "已注入" : "未注入"
        );
    }

    /**
     * 检查自动封装功能状态
     */
    @GetMapping("/auto-wrap-status")
    public String autoWrapStatus() {
        return "如果你看到这个字符串被包装在ApiResult中，说明自动封装功能正常工作";
    }

    // ==================== 自动封装测试接口 ====================

    /**
     * 测试返回String类型 - 应该自动封装为ApiResult
     */
    @GetMapping("/auto-wrap/string")
    public String testAutoWrapString() {
        return "这是一个字符串，应该被自动封装为ApiResult";
    }

    /**
     * 测试返回Integer类型 - 应该自动封装为ApiResult
     */
    @GetMapping("/auto-wrap/integer")
    public Integer testAutoWrapInteger() {
        return 12345;
    }

    /**
     * 测试返回Boolean类型 - 应该自动封装为ApiResult
     */
    @GetMapping("/auto-wrap/boolean")
    public Boolean testAutoWrapBoolean() {
        return true;
    }

    /**
     * 测试返回void类型 - 应该自动封装为ApiResult
     */
    @PostMapping("/auto-wrap/void")
    public void testAutoWrapVoid() {
        System.out.println("执行了一个void方法");
    }

    /**
     * 测试返回POJO对象 - 应该自动封装为ApiResult
     */
    @GetMapping("/auto-wrap/pojo")
    public TestUser testAutoWrapPojo() {
        return new TestUser(1L, "张三", "<EMAIL>", 25);
    }

    /**
     * 测试返回List集合 - 应该自动封装为ApiResult
     */
    @GetMapping("/auto-wrap/list")
    public java.util.List<TestUser> testAutoWrapList() {
        return java.util.Arrays.asList(
            new TestUser(1L, "张三", "<EMAIL>", 25),
            new TestUser(2L, "李四", "<EMAIL>", 30),
            new TestUser(3L, "王五", "<EMAIL>", 28)
        );
    }

    /**
     * 测试返回Map - 应该自动封装为ApiResult
     */
    @GetMapping("/auto-wrap/map")
    public java.util.Map<String, Object> testAutoWrapMap() {
        java.util.Map<String, Object> map = new java.util.HashMap<>();
        map.put("message", "这是一个Map对象");
        map.put("timestamp", System.currentTimeMillis());
        map.put("success", true);
        return map;
    }

    /**
     * 测试手动返回ApiResult - 不应该重复封装
     */
//    @GetMapping("/auto-wrap/manual-result")
//    public lhx.project91.common.result.ApiResult<TestUser> testManualApiResult() {
//        TestUser user = new TestUser(1L, "手动封装", "<EMAIL>", 35);
//        return lhx.project91.common.result.ApiResult.success(user, "这是手动封装的ApiResult");
//    }

    /**
     * 测试用户类
     */
    public static class TestUser {
        private Long id;
        private String name;
        private String email;
        private Integer age;

        public TestUser() {}

        public TestUser(Long id, String name, String email, Integer age) {
            this.id = id;
            this.name = name;
            this.email = email;
            this.age = age;
        }

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }
} 
