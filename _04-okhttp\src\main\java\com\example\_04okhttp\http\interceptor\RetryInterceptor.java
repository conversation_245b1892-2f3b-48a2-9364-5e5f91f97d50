package com.example._04okhttp.http.interceptor;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import java.io.IOException;

/**
 * 重试拦截器
 * 在请求失败时自动重试
 */
@Slf4j
public class RetryInterceptor implements Interceptor {
    
    private final int maxRetries;
    
    public RetryInterceptor() {
        this.maxRetries = 3;
    }
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        IOException lastException = null;
        
        for (int i = 0; i <= maxRetries; i++) {
            try {
                response = chain.proceed(request);
                if (response.isSuccessful()) {
                    return response;
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                lastException = e;
                log.warn("请求失败，第{}次重试: {}", i + 1, e.getMessage());
                
                if (i == maxRetries) {
                    throw e;
                }
                
                try {
                    Thread.sleep(1000 * (i + 1)); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("重试被中断", ie);
                }
            }
        }
        
        if (lastException != null) {
            throw lastException;
        }
        
        return response;
    }
}