server:
  port: 88
spring:
  application:
    name: nacos-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true # 开启服务发现自动路由
    nacos:
      discovery:
        # 服务发现
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631  # 服务发现命名空间
        enabled: true #开启拉取功能
        register-enabled: false #关闭注册功能 (✅ 可以调用别人（拉取服务列表）  &&&  ❌ 别人调不到它（注册中心里没有它）)
#      config:
#        # 配置中心
#        server-addr: 127.0.0.1:8848
#        namespace: c2391875-916d-476c-b72e-f4d9db8f5631 # 配置中心命名空间
#        file-extension: yaml
#        shared-configs:
#          - data-id: other-config.yaml
#            group: OTHER_GROUP
#            refresh: true