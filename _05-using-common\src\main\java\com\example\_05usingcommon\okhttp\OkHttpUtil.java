package com.example._05usingcommon.okhttp;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp工具类
 * 临时替代91项目的OkHttpUtil
 */
@Component
public class OkHttpUtil {

    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;

    public OkHttpUtil() {
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * GET请求
     */
    public String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                return response.body().string();
            }
            return "";
        }
    }

    /**
     * GET请求，返回HttpResult
     */
    public HttpResult<String> getForResult(String url) {
        long startTime = System.currentTimeMillis();
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                long duration = System.currentTimeMillis() - startTime;
                String body = response.body() != null ? response.body().string() : "";
                
                return HttpResult.success(body, response.code(), duration);
            }
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            return HttpResult.failure(500, e.getMessage(), duration);
        }
    }

    /**
     * POST JSON请求
     */
    public String postJson(String url, String jsonBody) throws IOException {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                return response.body().string();
            }
            return "";
        }
    }

    /**
     * POST对象请求
     */
    public String postObject(String url, Object obj) throws IOException {
        String jsonBody = objectMapper.writeValueAsString(obj);
        return postJson(url, jsonBody);
    }

    /**
     * POST请求，返回HttpResult
     */
    public HttpResult<String> postForResult(String url, String jsonBody) {
        long startTime = System.currentTimeMillis();
        try {
            MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(jsonBody, mediaType);

            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                long duration = System.currentTimeMillis() - startTime;
                String responseBody = response.body() != null ? response.body().string() : "";
                
                return HttpResult.success(responseBody, response.code(), duration);
            }
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            return HttpResult.failure(500, e.getMessage(), duration);
        }
    }
}
