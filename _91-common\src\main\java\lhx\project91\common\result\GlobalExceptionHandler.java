package lhx.project91.common.result;

import lhx.project91.okhttp.exception.OkHttpException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 
 * <p>统一处理系统中的各种异常，并转换为标准的ApiResult格式：</p>
 * <ul>
 *   <li>参数校验异常</li>
 *   <li>业务异常</li>
 *   <li>系统异常</li>
 *   <li>HTTP客户端异常</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数校验异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("参数校验失败: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        return ApiResult.<Void>failure(ResultCode.PARAM_ERROR, errorMessage)
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理参数绑定异常（@ModelAttribute）
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleBindException(BindException e, HttpServletRequest request) {
        log.warn("参数绑定失败: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        return ApiResult.<Void>failure(ResultCode.PARAM_ERROR, errorMessage)
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理约束校验异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.warn("约束校验失败: {}", e.getMessage());

        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));

        return ApiResult.<Void>failure(ResultCode.PARAM_ERROR, errorMessage)
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        log.warn("缺少请求参数: {}", e.getMessage());

        String errorMessage = String.format("缺少必要参数: %s", e.getParameterName());

        return ApiResult.<Void>failure(ResultCode.PARAM_MISSING, errorMessage)
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.warn("参数类型不匹配: {}", e.getMessage());

        String errorMessage = String.format("参数 %s 类型错误，期望类型: %s",
                e.getName(), e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "未知");

        return ApiResult.<Void>failure(ResultCode.PARAM_FORMAT_ERROR, errorMessage)
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理OkHttp异常
     */
    @ExceptionHandler(OkHttpException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handleOkHttpException(OkHttpException e, HttpServletRequest request) {
        log.error("HTTP请求异常: {}", e.getMessage(), e);

        ResultCode resultCode = e.getStatusCode() > 0 ?
                mapHttpStatusToResultCode(e.getStatusCode()) : ResultCode.NETWORK_ERROR;

        return ApiResult.<Void>failure(resultCode, e.getMessage(), e.getUrl())
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ApiResult<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {}", e.getMessage());
        
        return ApiResult.<Void>failure(e.getResultCode(), e.getMessage(), e.getErrorDetail())
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常: {}", e.getMessage(), e);

        return ApiResult.<Void>failure(ResultCode.SYSTEM_ERROR, "系统内部错误", e.getMessage())
                .withTraceId(getTraceId(request));
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常: {}", e.getMessage(), e);

        return ApiResult.<Void>failure(ResultCode.SYSTEM_ERROR, "系统异常", e.getMessage())
                .withTraceId(getTraceId(request));
    }

    /**
     * 获取请求追踪ID
     */
    private String getTraceId(HttpServletRequest request) {
        String traceId = request.getHeader("X-Trace-Id");
        if (traceId == null) {
            traceId = request.getHeader("traceId");
        }
        if (traceId == null) {
            traceId = String.valueOf(System.currentTimeMillis());
        }
        return traceId;
    }

    /**
     * 将HTTP状态码映射到ResultCode
     */
    private ResultCode mapHttpStatusToResultCode(int httpStatus) {
        switch (httpStatus) {
            case 400:
                return ResultCode.PARAM_ERROR;
            case 401:
                return ResultCode.UNAUTHORIZED;
            case 403:
                return ResultCode.FORBIDDEN;
            case 404:
                return ResultCode.NOT_FOUND;
            case 408:
                return ResultCode.TIMEOUT;
            case 409:
                return ResultCode.CONFLICT;
            case 429:
                return ResultCode.TOO_FREQUENT;
            case 500:
                return ResultCode.SYSTEM_ERROR;
            case 503:
                return ResultCode.SERVICE_UNAVAILABLE;
            case 504:
                return ResultCode.THIRD_PARTY_TIMEOUT;
            default:
                return httpStatus >= 500 ? ResultCode.SYSTEM_ERROR : ResultCode.BUSINESS_ERROR;
        }
    }
}
