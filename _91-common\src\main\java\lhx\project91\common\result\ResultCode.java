package lhx.project91.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一结果码枚举
 * 
 * <p>定义了系统中所有可能的响应状态码，包括：</p>
 * <ul>
 *   <li>通用状态码（1xxx）</li>
 *   <li>业务状态码（2xxx）</li>
 *   <li>客户端错误（4xxx）</li>
 *   <li>服务器错误（5xxx）</li>
 * </ul>
 * 
 * <p>每个状态码包含：</p>
 * <ul>
 *   <li>业务状态码：用于业务逻辑判断</li>
 *   <li>HTTP状态码：用于HTTP响应</li>
 *   <li>消息：用于前端显示</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // ==================== 通用状态码 1xxx ====================
    
    /**
     * 操作成功
     */
    SUCCESS("1000", 200, "操作成功"),
    
    /**
     * 操作失败
     */
    FAILURE("1001", 500, "操作失败"),
    
    /**
     * 部分成功
     */
    PARTIAL_SUCCESS("1002", 200, "部分操作成功"),

    // ==================== 参数相关 2xxx ====================
    
    /**
     * 参数错误
     */
    PARAM_ERROR("2000", 400, "参数错误"),
    
    /**
     * 参数缺失
     */
    PARAM_MISSING("2001", 400, "必要参数缺失"),
    
    /**
     * 参数格式错误
     */
    PARAM_FORMAT_ERROR("2002", 400, "参数格式错误"),
    
    /**
     * 参数值无效
     */
    PARAM_INVALID("2003", 400, "参数值无效"),

    // ==================== 认证授权相关 3xxx ====================
    
    /**
     * 未认证
     */
    UNAUTHORIZED("3000", 401, "未认证，请先登录"),
    
    /**
     * 认证失败
     */
    AUTH_FAILED("3001", 401, "认证失败"),
    
    /**
     * 无权限
     */
    FORBIDDEN("3002", 403, "无权限访问"),
    
    /**
     * Token无效
     */
    TOKEN_INVALID("3003", 401, "Token无效"),
    
    /**
     * Token过期
     */
    TOKEN_EXPIRED("3004", 401, "Token已过期"),

    // ==================== 资源相关 4xxx ====================
    
    /**
     * 资源不存在
     */
    NOT_FOUND("4000", 404, "资源不存在"),
    
    /**
     * 资源已存在
     */
    ALREADY_EXISTS("4001", 409, "资源已存在"),
    
    /**
     * 资源被锁定
     */
    RESOURCE_LOCKED("4002", 423, "资源被锁定"),
    
    /**
     * 资源冲突
     */
    CONFLICT("4003", 409, "资源冲突"),

    // ==================== 业务相关 5xxx ====================
    
    /**
     * 业务处理失败
     */
    BUSINESS_ERROR("5000", 500, "业务处理失败"),
    
    /**
     * 数据不一致
     */
    DATA_INCONSISTENT("5001", 500, "数据不一致"),
    
    /**
     * 状态错误
     */
    STATUS_ERROR("5002", 400, "状态错误"),
    
    /**
     * 操作频繁
     */
    TOO_FREQUENT("5003", 429, "操作过于频繁，请稍后再试"),

    // ==================== 系统相关 6xxx ====================
    
    /**
     * 系统错误
     */
    SYSTEM_ERROR("6000", 500, "系统内部错误"),
    
    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE("6001", 503, "服务暂时不可用"),
    
    /**
     * 网络错误
     */
    NETWORK_ERROR("6002", 500, "网络连接错误"),
    
    /**
     * 超时
     */
    TIMEOUT("6003", 408, "请求超时"),
    
    /**
     * 数据库错误
     */
    DATABASE_ERROR("6004", 500, "数据库操作失败"),

    // ==================== 文件相关 7xxx ====================
    
    /**
     * 文件不存在
     */
    FILE_NOT_FOUND("7000", 404, "文件不存在"),
    
    /**
     * 文件格式错误
     */
    FILE_FORMAT_ERROR("7001", 400, "文件格式不支持"),
    
    /**
     * 文件过大
     */
    FILE_TOO_LARGE("7002", 413, "文件大小超出限制"),
    
    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAILED("7003", 500, "文件上传失败"),
    
    /**
     * 文件下载失败
     */
    FILE_DOWNLOAD_FAILED("7004", 500, "文件下载失败"),

    // ==================== 第三方服务相关 8xxx ====================
    
    /**
     * 第三方服务错误
     */
    THIRD_PARTY_ERROR("8000", 500, "第三方服务错误"),
    
    /**
     * 第三方服务超时
     */
    THIRD_PARTY_TIMEOUT("8001", 504, "第三方服务超时"),
    
    /**
     * 第三方服务不可用
     */
    THIRD_PARTY_UNAVAILABLE("8002", 503, "第三方服务不可用"),

    // ==================== 限流熔断相关 9xxx ====================
    
    /**
     * 请求被限流
     */
    RATE_LIMITED("9000", 429, "请求过于频繁，已被限流"),
    
    /**
     * 服务熔断
     */
    CIRCUIT_BREAKER("9001", 503, "服务熔断，请稍后重试"),
    
    /**
     * 服务降级
     */
    SERVICE_DEGRADED("9002", 200, "服务降级");

    /**
     * 业务状态码
     */
    private final String code;

    /**
     * HTTP状态码
     */
    private final Integer httpStatus;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 根据业务状态码查找ResultCode
     * 
     * @param code 业务状态码
     * @return ResultCode枚举，如果未找到则返回null
     */
    public static ResultCode getByCode(String code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }

    /**
     * 判断是否为成功状态码
     * 
     * @return true表示成功
     */
    public boolean isSuccess() {
        return this == SUCCESS || this == PARTIAL_SUCCESS || this == SERVICE_DEGRADED;
    }

    /**
     * 判断是否为客户端错误
     * 
     * @return true表示客户端错误
     */
    public boolean isClientError() {
        return httpStatus >= 400 && httpStatus < 500;
    }

    /**
     * 判断是否为服务器错误
     * 
     * @return true表示服务器错误
     */
    public boolean isServerError() {
        return httpStatus >= 500;
    }
}
