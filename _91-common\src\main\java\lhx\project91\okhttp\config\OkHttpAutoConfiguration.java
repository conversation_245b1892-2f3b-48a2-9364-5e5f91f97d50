package lhx.project91.okhttp.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lhx.project91.common.result.GlobalExceptionHandler;
import lhx.project91.common.result.GlobalResponseHandler;
import lhx.project91.common.result.GlobalResponseProperties;
import lhx.project91.okhttp.util.OkHttpTemplate;
import lhx.project91.okhttp.util.OkHttpUtil;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * OkHttp 自动配置类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Configuration
@ConditionalOnClass(OkHttpClient.class)
@ConditionalOnProperty(prefix = "okhttp", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties({OkHttpProperties.class, GlobalResponseProperties.class})
public class OkHttpAutoConfiguration {

    /**
     * 配置 OkHttpClient Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public OkHttpClient okHttpClient(OkHttpProperties properties) {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        
        // 设置超时时间
        builder.connectTimeout(properties.getConnectTimeout().toMillis(), TimeUnit.MILLISECONDS);
        builder.readTimeout(properties.getReadTimeout().toMillis(), TimeUnit.MILLISECONDS);
        builder.writeTimeout(properties.getWriteTimeout().toMillis(), TimeUnit.MILLISECONDS);
        builder.callTimeout(properties.getCallTimeout().toMillis(), TimeUnit.MILLISECONDS);
        
        // 配置连接池
        OkHttpProperties.ConnectionPool poolConfig = properties.getConnectionPool();
        ConnectionPool connectionPool = new ConnectionPool(
            poolConfig.getMaxIdleConnections(),
            poolConfig.getKeepAliveDuration().toMillis(),
            TimeUnit.MILLISECONDS
        );
        builder.connectionPool(connectionPool);
        
        // 配置重试
        builder.retryOnConnectionFailure(properties.getRetry().isEnabled());
        
        // 配置日志拦截器
        if (properties.getLogging().isEnabled()) {
            // 这里可以添加日志拦截器
            // builder.addInterceptor(new HttpLoggingInterceptor());
        }
        
        return builder.build();
    }

    /**
     * 配置 ObjectMapper Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    /**
     * 配置 OkHttpUtil Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public OkHttpUtil okHttpUtil(OkHttpClient okHttpClient, ObjectMapper objectMapper) {
        return new OkHttpUtil(okHttpClient, objectMapper);
    }

    /**
     * 配置 OkHttpTemplate Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public OkHttpTemplate okHttpTemplate(OkHttpUtil okHttpUtil) {
        return new OkHttpTemplate(okHttpUtil);
    }

    /**
     * 全局响应处理器 Bean
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(name = "org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice")
    public GlobalResponseHandler globalResponseHandler() {
        GlobalResponseProperties properties = GlobalResponseProperties.defaultConfig();
        return new GlobalResponseHandler(properties);
    }

    /**
     * 全局异常处理器 Bean
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(name = "org.springframework.web.bind.annotation.RestControllerAdvice")
    @ConditionalOnProperty(name = "project91.global-response.exception-handler-enabled", havingValue = "true", matchIfMissing = true)
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }
}
