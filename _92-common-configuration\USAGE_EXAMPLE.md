# MinIO服务使用示例

## 在其他项目中使用MinIO服务

### 1. 添加依赖

在其他项目的 `pom.xml` 中添加Feign依赖：

```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
</dependency>
```

### 2. 启用Feign客户端

在启动类上添加注解：

```java
@SpringBootApplication
@EnableFeignClients
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 复制Feign客户端接口

将 `MinioFeignClient.java` 复制到你的项目中，或者创建类似的接口：

```java
@FeignClient(name = "minio-service", url = "http://localhost:8086", path = "/api/minio")
public interface MinioFeignClient {
    
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<FileInfo> uploadFile(@RequestPart("file") MultipartFile file);
    
    @GetMapping("/files")
    Result<List<FileInfo>> listFiles();
    
    @DeleteMapping("/file/{objectName}")
    Result<Void> deleteFile(@PathVariable String objectName);
}
```

### 4. 创建服务类

```java
@Service
@RequiredArgsConstructor
public class FileService {
    
    private final MinioFeignClient minioFeignClient;
    
    public FileInfo uploadFile(MultipartFile file) {
        Result<FileInfo> result = minioFeignClient.uploadFile(file);
        if (result.getCode() == 200) {
            return result.getData();
        }
        throw new RuntimeException("文件上传失败: " + result.getMessage());
    }
    
    public List<FileInfo> listFiles() {
        Result<List<FileInfo>> result = minioFeignClient.listFiles();
        if (result.getCode() == 200) {
            return result.getData();
        }
        throw new RuntimeException("获取文件列表失败: " + result.getMessage());
    }
    
    public void deleteFile(String objectName) {
        Result<Void> result = minioFeignClient.deleteFile(objectName);
        if (result.getCode() != 200) {
            throw new RuntimeException("删除文件失败: " + result.getMessage());
        }
    }
}
```

### 5. 在控制器中使用

```java
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
public class FileController {
    
    private final FileService fileService;
    
    @PostMapping("/upload")
    public ResponseEntity<FileInfo> uploadFile(@RequestParam("file") MultipartFile file) {
        FileInfo fileInfo = fileService.uploadFile(file);
        return ResponseEntity.ok(fileInfo);
    }
    
    @GetMapping("/list")
    public ResponseEntity<List<FileInfo>> listFiles() {
        List<FileInfo> files = fileService.listFiles();
        return ResponseEntity.ok(files);
    }
    
    @DeleteMapping("/{objectName}")
    public ResponseEntity<Void> deleteFile(@PathVariable String objectName) {
        fileService.deleteFile(objectName);
        return ResponseEntity.ok().build();
    }
}
```

## 使用RestTemplate调用

如果不使用Feign，也可以使用RestTemplate：

```java
@Service
public class FileService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    private final String minioServiceUrl = "http://localhost:8086/api/minio";
    
    public FileInfo uploadFile(MultipartFile file) {
        String url = minioServiceUrl + "/upload";
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = 
            new HttpEntity<>(body, headers);
            
        ResponseEntity<Result> response = restTemplate.postForEntity(url, requestEntity, Result.class);
        
        if (response.getBody().getCode() == 200) {
            return (FileInfo) response.getBody().getData();
        }
        throw new RuntimeException("文件上传失败");
    }
    
    public List<FileInfo> listFiles() {
        String url = minioServiceUrl + "/files";
        ResponseEntity<Result> response = restTemplate.getForEntity(url, Result.class);
        
        if (response.getBody().getCode() == 200) {
            return (List<FileInfo>) response.getBody().getData();
        }
        throw new RuntimeException("获取文件列表失败");
    }
}
```

## 配置RestTemplate Bean

```java
@Configuration
public class RestTemplateConfig {
    
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

## 错误处理

建议添加全局异常处理：

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(FeignException.class)
    public ResponseEntity<String> handleFeignException(FeignException e) {
        return ResponseEntity.status(e.status()).body("调用MinIO服务失败: " + e.getMessage());
    }
    
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<String> handleRuntimeException(RuntimeException e) {
        return ResponseEntity.status(500).body("服务异常: " + e.getMessage());
    }
}
```

## 注意事项

1. 确保MinIO服务正在运行
2. 检查网络连接
3. 配置正确的服务地址
4. 处理异常情况
5. 考虑添加重试机制
