# ==================== 默认测试配置 ====================
# 这个段落的配置对所有profile都生效
okhttp:
  enabled: true
  connect-timeout: 5s
  read-timeout: 10s
  write-timeout: 10s
  call-timeout: 20s
  connection-pool:
    max-idle-connections: 2
    keep-alive-duration: 1m
  retry:
    enabled: true
    max-retries: 1
    retry-interval: 100ms
  logging:
    enabled: true
    level: BASIC

# 全局响应处理器配置（测试环境禁用Web相关功能）
project91:
  global-response:
    enabled: false                   # 测试环境禁用全局响应处理器
    exception-handler-enabled: false # 测试环境禁用全局异常处理器

# 日志配置
logging:
  level:
    lhx.project91.okhttp: DEBUG
    okhttp3: DEBUG
    org.springframework.web: WARN    # 降低Web相关日志级别

# ==================== unit-test profile ====================
---
spring:
  profiles: unit-test

# 单元测试专用配置（会覆盖上面的默认配置）
okhttp:
  connect-timeout: 1s
  read-timeout: 3s
  retry:
    max-retries: 0  # 单元测试不重试
  logging:
    level: NONE     # 单元测试不输出HTTP日志

logging:
  level:
    lhx.project91.okhttp: WARN

# ==================== integration-test profile ====================
---
spring:
  profiles: integration-test

# 集成测试专用配置
okhttp:
  connect-timeout: 10s
  read-timeout: 30s
  retry:
    max-retries: 2
  logging:
    level: HEADERS

logging:
  level:
    lhx.project91.okhttp: INFO
