# OkHttp Spring Boot Starter

这是一个基于 OkHttp 的 Spring Boot Starter，提供了简单易用的 HTTP 客户端工具类和模板类。

## 功能特性

- 🚀 **开箱即用** - 自动配置，无需手动创建 OkHttpClient
- 🔧 **灵活配置** - 支持通过 application.yml 配置连接池、超时等参数
- 📦 **多种API** - 提供 OkHttpUtil 工具类和 OkHttpTemplate 模板类
- 🔄 **同步异步** - 支持同步和异步请求
- 📊 **结果封装** - 统一的 HttpResult 返回格式
- 🎯 **泛型支持** - 支持直接返回指定类型对象
- 📝 **JSON处理** - 集成 Jackson 进行 JSON 序列化/反序列化
- ⚡ **高性能** - 基于 OkHttp 高性能 HTTP 客户端
- 🛡️ **异常处理** - 统一的异常处理机制

## 环境要求

- JDK 17+
- Spring Boot 2.7+
- Maven 3.6+

## 快速开始

### 1. 添加依赖

在其他项目的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>lhx.91project</groupId>
    <artifactId>okhttp-spring-boot-starter</artifactId>
    <version>2.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置参数（可选）

在 `application.yml` 中配置 OkHttp 参数：

```yaml
okhttp:
  enabled: true
  connect-timeout: 10s
  read-timeout: 30s
  write-timeout: 30s
  call-timeout: 60s
  connection-pool:
    max-idle-connections: 5
    keep-alive-duration: 5m
  retry:
    enabled: true
    max-retries: 3
    retry-interval: 1s
  logging:
    enabled: true
    level: BASIC
```

### 3. 使用示例

#### 使用 OkHttpUtil（工具类方式）

```java
@Service
public class ApiService {

    @Autowired
    private OkHttpUtil okHttpUtil;

    // GET 请求
    public String getData() {
        return okHttpUtil.get("https://api.example.com/data");
    }

    // POST JSON 请求
    public String postData(Object data) {
        return okHttpUtil.postObject("https://api.example.com/data", data);
    }

    // 获取指定类型对象
    public User getUser(Long id) {
        return okHttpUtil.getForObject("https://api.example.com/users/" + id, User.class);
    }

    // 异步请求
    public CompletableFuture<HttpResult<String>> getDataAsync() {
        return okHttpUtil.getAsyncFuture("https://api.example.com/data");
    }

    // 带请求头的GET请求
    public HttpResult<String> getDataWithHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer token");
        headers.put("Content-Type", "application/json");
        return okHttpUtil.getForResult("https://api.example.com/data", headers);
    }

    // POST表单请求
    public String postForm() {
        Map<String, String> formData = new HashMap<>();
        formData.put("username", "admin");
        formData.put("password", "123456");
        return okHttpUtil.postForm("https://api.example.com/login", formData);
    }
}
```

#### 使用 OkHttpTemplate（模板类方式）

```java
@Service
public class ApiService {

    @Autowired
    private OkHttpTemplate okHttpTemplate;

    // GET 请求返回 HttpResult
    public HttpResult<String> getData() {
        return okHttpTemplate.getForString("https://api.example.com/data");
    }

    // GET 请求返回指定类型对象
    public HttpResult<User> getUser(Long id) {
        return okHttpTemplate.getForObject("https://api.example.com/users/" + id, User.class);
    }

    // POST 请求
    public HttpResult<String> postData(Object data) {
        return okHttpTemplate.postForString("https://api.example.com/data", data);
    }

    // 异步GET请求
    public CompletableFuture<HttpResult<String>> getDataAsync() {
        return okHttpTemplate.getAsyncForString("https://api.example.com/data");
    }

    // DELETE请求
    public HttpResult<String> deleteData(Long id) {
        return okHttpTemplate.deleteForString("https://api.example.com/data/" + id);
    }
}
```

## API 文档

### OkHttpUtil 主要方法

#### GET 请求
- `get(String url)` - GET请求返回字符串
- `getForResult(String url)` - GET请求返回HttpResult
- `getForObject(String url, Class<T> clazz)` - GET请求返回指定类型对象
- `getAsyncFuture(String url)` - 异步GET请求

#### POST 请求
- `postJson(String url, String jsonBody)` - POST JSON请求
- `postObject(String url, Object object)` - POST对象（自动转JSON）
- `postForm(String url, Map<String, String> formData)` - POST表单请求
- `postJsonAsyncFuture(String url, String jsonBody)` - 异步POST请求

#### PUT/DELETE 请求
- `putJson(String url, String jsonBody)` - PUT JSON请求
- `delete(String url)` - DELETE请求

### OkHttpTemplate 主要方法

#### GET 请求
- `getForString(String url)` - GET请求返回字符串的HttpResult
- `getForObject(String url, Class<T> responseType)` - GET请求返回对象的HttpResult

#### POST 请求
- `postForString(String url, Object requestBody)` - POST请求返回字符串的HttpResult
- `postForObject(String url, Object requestBody, Class<T> responseType)` - POST请求返回对象的HttpResult

### HttpResult 结果封装

```java
public class HttpResult<T> {
    private boolean success;        // 是否成功
    private int statusCode;         // HTTP状态码
    private String statusMessage;   // 状态消息
    private T data;                 // 响应数据
    private String errorMessage;    // 错误信息
    private Map<String, String> headers; // 响应头
    private String url;             // 请求URL
    private String method;          // 请求方法
    private long duration;          // 请求耗时（毫秒）
    private LocalDateTime requestTime; // 请求时间
}
```

## 配置说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `okhttp.enabled` | `true` | 是否启用OkHttp |
| `okhttp.connect-timeout` | `10s` | 连接超时时间 |
| `okhttp.read-timeout` | `30s` | 读取超时时间 |
| `okhttp.write-timeout` | `30s` | 写入超时时间 |
| `okhttp.call-timeout` | `60s` | 调用超时时间 |
| `okhttp.connection-pool.max-idle-connections` | `5` | 最大空闲连接数 |
| `okhttp.connection-pool.keep-alive-duration` | `5m` | 连接保持活跃时间 |
| `okhttp.retry.enabled` | `true` | 是否启用重试 |
| `okhttp.retry.max-retries` | `3` | 最大重试次数 |
| `okhttp.retry.retry-interval` | `1s` | 重试间隔 |
| `okhttp.logging.enabled` | `true` | 是否启用请求日志 |
| `okhttp.logging.level` | `BASIC` | 日志级别 |

## 异常处理

框架提供了统一的异常处理：

```java
try {
    String result = okHttpUtil.get("https://api.example.com/data");
} catch (OkHttpException e) {
    log.error("HTTP请求失败: {}", e.getMessage());
    // 可以获取状态码、URL等信息
    int statusCode = e.getStatusCode();
    String url = e.getUrl();
    String method = e.getMethod();
}
```

## 最佳实践

1. **使用HttpResult**: 推荐使用返回HttpResult的方法，可以获取完整的响应信息
2. **异步处理**: 对于耗时操作，使用异步方法避免阻塞
3. **异常处理**: 合理处理OkHttpException异常
4. **配置调优**: 根据实际需求调整连接池和超时配置
5. **日志监控**: 启用日志记录，便于问题排查

## 版本历史

- **2.0.0-SNAPSHOT**: 重构版本，支持Spring Boot 2.7+，增加更多功能
- **1.0.0**: 初始版本

## 许可证

MIT License
