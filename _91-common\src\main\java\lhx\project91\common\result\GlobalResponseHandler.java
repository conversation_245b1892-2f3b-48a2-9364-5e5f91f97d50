package lhx.project91.common.result;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应处理器
 *
 * <p>自动将Controller返回的数据包装为ApiResult格式，支持：</p>
 * <ul>
 *   <li>自动包装普通返回值（POJO、基本类型、集合等）</li>
 *   <li>保持已包装的ApiResult不变</li>
 *   <li>支持void类型的无返回值处理</li>
 *   <li>可通过配置启用/禁用自动包装功能</li>
 *   <li>支持排除特定包路径或注解</li>
 * </ul>
 *
 * <p>支持的返回类型：</p>
 * <ul>
 *   <li>普通POJO/VO对象（如User、Order等）</li>
 *   <li>基本数据类型（String、Integer、Boolean等）</li>
 *   <li>集合类型（List、Map等）</li>
 *   <li>void类型（无返回值）</li>
 * </ul>
 *
 * <p>使用方式：</p>
 * <pre>{@code
 * @RestController
 * public class UserController {
 *
 *     @GetMapping("/user/{id}")
 *     public User getUser(@PathVariable Long id) {
 *         // 返回User对象，会自动包装为ApiResult<User>
 *         return userService.findById(id);
 *     }
 *
 *     @GetMapping("/users")
 *     public ApiResult<List<User>> getUsers() {
 *         // 已经是ApiResult，不会重复包装
 *         List<User> users = userService.findAll();
 *         return ApiResult.success(users);
 *     }
 *
 *     @PostMapping("/users/{id}/activate")
 *     public void activateUser(@PathVariable Long id) {
 *         // void返回值，会包装为ApiResult.success()
 *         userService.activate(id);
 *     }
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
@ConditionalOnProperty(name = "project91.global-response.enabled", havingValue = "true", matchIfMissing = true)
public class GlobalResponseHandler implements ResponseBodyAdvice<Object> {

    private final GlobalResponseProperties properties;

    /**
     * 判断是否需要处理响应
     *
     * @param returnType 返回类型
     * @param converterType 转换器类型
     * @return true表示需要处理
     */
    @Override
    public boolean supports(@NonNull MethodParameter returnType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        // 排除已经是ApiResult类型的返回值
        Class<?> parameterType = returnType.getParameterType();
        if (ApiResult.class.isAssignableFrom(parameterType)) {
            return false;
        }

        // 使用配置检查是否应该排除
        String declaringClassName = returnType.getDeclaringClass().getName();
        if (properties.shouldExcludeClass(declaringClassName)) {
            return false;
        }

        return true;
    }

    /**
     * 处理响应体
     *
     * @param body 原始响应体
     * @param returnType 返回类型
     * @param selectedContentType 选择的内容类型
     * @param selectedConverterType 选择的转换器类型
     * @param request 请求对象
     * @param response 响应对象
     * @return 处理后的响应体
     */
    @Override
    public Object beforeBodyWrite(@Nullable Object body, @NonNull MethodParameter returnType, @NonNull MediaType selectedContentType,
                                @NonNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                @NonNull ServerHttpRequest request, @NonNull ServerHttpResponse response) {
        
        // 如果已经是ApiResult类型，直接返回（双重保险）
        if (body instanceof ApiResult) {
            return body;
        }

        // 处理void类型或null返回值
        if (body == null) {
            if (!properties.isHandleVoidResponse()) {
                return null;
            }
            return createApiResult(null, request);
        }

        // 处理String类型
        if (body instanceof String) {
            if (!properties.isHandleStringResponse()) {
                return body;
            }
            String stringBody = (String) body;
            // 如果是JSON格式的ApiResult字符串，直接返回
            if (stringBody.trim().startsWith("{") && stringBody.contains("\"success\"")) {
                return stringBody;
            }
            return createApiResult(stringBody, request);
        }

        // 处理基本数据类型的包装类
        if (isPrimitiveWrapper(body.getClass())) {
            if (!properties.isHandlePrimitiveResponse()) {
                return body;
            }
            return createApiResult(body, request);
        }

        // 处理集合类型
        if (body instanceof java.util.Collection || body instanceof java.util.Map) {
            if (!properties.isHandleCollectionResponse()) {
                return body;
            }
            return createApiResult(body, request);
        }

        // 处理数组类型
        if (body.getClass().isArray()) {
            if (!properties.isHandleCollectionResponse()) {
                return body;
            }
            return createApiResult(body, request);
        }

        // 其他POJO类型自动包装为成功结果
        return createApiResult(body, request);
    }

    /**
     * 创建ApiResult并添加额外信息
     *
     * @param data 数据
     * @param request 请求对象
     * @return ApiResult
     */
    private ApiResult<Object> createApiResult(Object data, ServerHttpRequest request) {
        ApiResult<Object> result = ApiResult.success(data);

        // 添加追踪ID
        if (properties.isAutoTraceId()) {
            String traceId = getTraceId(request);
            if (traceId != null) {
                result.withTraceId(traceId);
            }
        }

        // 添加服务器信息
        if (properties.isAddServerInfo() && properties.getServerName() != null) {
            result.withServer(properties.getServerName());
        }

        // 添加API版本
        if (properties.getApiVersion() != null) {
            result.withVersion(properties.getApiVersion());
        }

        return result;
    }

    /**
     * 获取追踪ID
     *
     * @param request 请求对象
     * @return 追踪ID
     */
    private String getTraceId(ServerHttpRequest request) {
        String traceId = request.getHeaders().getFirst(properties.getTraceIdHeader());
        if (traceId == null) {
            traceId = request.getHeaders().getFirst("traceId");
        }
        if (traceId == null) {
            traceId = request.getHeaders().getFirst("X-Request-ID");
        }
        return traceId;
    }

    /**
     * 判断是否为基本数据类型的包装类
     *
     * @param clazz 类型
     * @return true表示是包装类
     */
    private boolean isPrimitiveWrapper(Class<?> clazz) {
        return clazz == Boolean.class ||
               clazz == Byte.class ||
               clazz == Character.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class;
    }
}
