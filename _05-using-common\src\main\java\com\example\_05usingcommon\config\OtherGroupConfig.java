package com.example._05usingcommon.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@Data
public class OtherGroupConfig {

    @Value("${custom.property:其他组默认值}")
    private String customProperty;

} 