package lhx.project91.okhttp;

import com.fasterxml.jackson.databind.ObjectMapper;
import lhx.project91.okhttp.config.OkHttpProperties;
import lhx.project91.okhttp.util.OkHttpTemplate;
import lhx.project91.okhttp.util.OkHttpUtil;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * 测试专用配置类
 * 只配置OkHttp相关的Bean，避免Web相关依赖
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Configuration
@EnableConfigurationProperties(OkHttpProperties.class)
public class TestConfiguration {

    /**
     * 测试用的OkHttpClient
     */
    @Bean
    public OkHttpClient testOkHttpClient() {
        ConnectionPool connectionPool = new ConnectionPool(2, 1, TimeUnit.MINUTES);
        
        return new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .callTimeout(20, TimeUnit.SECONDS)
                .connectionPool(connectionPool)
                .build();
    }

    /**
     * 测试用的ObjectMapper
     */
    @Bean
    public ObjectMapper testObjectMapper() {
        return new ObjectMapper();
    }

    /**
     * 测试用的OkHttpUtil
     */
    @Bean
    public OkHttpUtil testOkHttpUtil(OkHttpClient okHttpClient, ObjectMapper objectMapper) {
        return new OkHttpUtil(okHttpClient, objectMapper);
    }

    /**
     * 测试用的OkHttpTemplate
     */
    @Bean
    public OkHttpTemplate testOkHttpTemplate(OkHttpUtil okHttpUtil) {
        return new OkHttpTemplate(okHttpUtil);
    }
}
