# 默认配置（所有profile都会加载）
server:
  port: 8080

okhttp:
  enabled: true
  connect-timeout: 10s

# 激活test profile
spring:
  profiles:
    active: test

---
# test profile的配置
spring:
  profiles: test

server:
  port: 8081

okhttp:
  connect-timeout: 5s
  read-timeout: 10s
  logging:
    enabled: true
    level: DEBUG

---
# prod profile的配置
spring:
  profiles: prod

server:
  port: 8082

okhttp:
  connect-timeout: 3s
  read-timeout: 15s
  logging:
    enabled: false

---
# dev profile的配置
spring:
  profiles: dev

server:
  port: 8083

okhttp:
  connect-timeout: 30s
  read-timeout: 60s
  logging:
    enabled: true
    level: BODY
