package com.example._04okhttp.http.interceptor;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import java.io.IOException;

/**
 * 日志拦截器
 * 记录请求和响应信息
 */
@Slf4j
public class LoggingInterceptor implements Interceptor {
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        long startTime = System.currentTimeMillis();
        
        // 记录请求信息
        log.info("发送请求: {} {}", request.method(), request.url());
        if (request.body() != null) {
            log.info("请求头: {}", request.headers());
        }
        
        Response response = chain.proceed(request);
        long endTime = System.currentTimeMillis();
        
        // 记录响应信息
        log.info("收到响应: {} {} ({}ms)", 
            response.code(), request.url(), endTime - startTime);
        
        return response;
    }
}