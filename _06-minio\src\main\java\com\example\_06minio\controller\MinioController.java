package com.example._06minio.controller;

import com.example._06minio.common.Result;
import com.example._06minio.entity.FileInfo;
import com.example._06minio.service.MinioService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * MinIO文件操作控制器
 */
@RestController
@RequestMapping("/api/minio")
@RequiredArgsConstructor
@Slf4j
public class MinioController {

    private final MinioService minioService;

    /**
     * 创建存储桶
     */
    @PostMapping("/bucket/{bucketName}")
    public Result<Void> createBucket(@PathVariable String bucketName) {
        minioService.createBucket(bucketName);
        return Result.success("存储桶创建成功");
    }

    /**
     * 检查存储桶是否存在
     */
    @GetMapping("/bucket/{bucketName}/exists")
    public Result<Boolean> bucketExists(@PathVariable String bucketName) {
        boolean exists = minioService.bucketExists(bucketName);
        return Result.success(exists);
    }

    /**
     * 获取所有存储桶
     */
    @GetMapping("/buckets")
    public Result<List<String>> getAllBuckets() {
        List<String> buckets = minioService.getAllBuckets();
        return Result.success(buckets);
    }

    /**
     * 删除存储桶
     */
    @DeleteMapping("/bucket/{bucketName}")
    public Result<Void> removeBucket(@PathVariable String bucketName) {
        minioService.removeBucket(bucketName);
        return Result.success("存储桶删除成功");
    }

    /**
     * 上传文件到指定存储桶
     */
    @PostMapping("/upload/{bucketName}")
    public Result<FileInfo> uploadFile(@RequestParam("file") MultipartFile file,
                                       @PathVariable String bucketName) {
        FileInfo fileInfo = minioService.uploadFile(file, bucketName);
        return Result.success("文件上传成功", fileInfo);
    }

    /**
     * 上传文件到默认存储桶
     */
    @PostMapping("/upload")
    public Result<FileInfo> uploadFile(@RequestParam("file") MultipartFile file) {
        FileInfo fileInfo = minioService.uploadFile(file);
        return Result.success("文件上传成功", fileInfo);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download/{bucketName}/{objectName}")
    public void downloadFile(@PathVariable String bucketName,
                             @PathVariable String objectName,
                             HttpServletResponse response) {
        try {
            InputStream inputStream = minioService.downloadFile(bucketName, objectName);
            
            // 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=" + URLEncoder.encode(objectName, StandardCharsets.UTF_8));

            // 将文件流写入响应
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.getOutputStream().flush();
            inputStream.close();
        } catch (Exception e) {
            log.error("下载文件失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 从默认存储桶下载文件
     */
    @GetMapping("/download/{objectName}")
    public void downloadFile(@PathVariable String objectName,
                             HttpServletResponse response) {
        try {
            InputStream inputStream = minioService.downloadFile(objectName);
            
            // 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=" + URLEncoder.encode(objectName, StandardCharsets.UTF_8));

            // 将文件流写入响应
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.getOutputStream().flush();
            inputStream.close();
        } catch (Exception e) {
            log.error("下载文件失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/file/{bucketName}/{objectName}")
    public Result<Void> deleteFile(@PathVariable String bucketName,
                                   @PathVariable String objectName) {
        minioService.deleteFile(bucketName, objectName);
        return Result.success("文件删除成功");
    }

    /**
     * 从默认存储桶删除文件
     */
    @DeleteMapping("/file/{objectName}")
    public Result<Void> deleteFile(@PathVariable String objectName) {
        minioService.deleteFile(objectName);
        return Result.success("文件删除成功");
    }

    /**
     * 获取文件预签名URL
     */
    @GetMapping("/presigned-url/{bucketName}/{objectName}")
    public Result<String> getPresignedUrl(@PathVariable String bucketName,
                                          @PathVariable String objectName,
                                          @RequestParam(defaultValue = "3600") int expires) {
        String url = minioService.getPresignedUrl(bucketName, objectName, expires);
        return Result.success(url);
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/file-info/{bucketName}/{objectName}")
    public Result<FileInfo> getFileInfo(@PathVariable String bucketName,
                                        @PathVariable String objectName) {
        FileInfo fileInfo = minioService.getFileInfo(bucketName, objectName);
        return Result.success(fileInfo);
    }

    /**
     * 列出存储桶中的所有文件
     */
    @GetMapping("/files/{bucketName}")
    public Result<List<FileInfo>> listFiles(@PathVariable String bucketName) {
        List<FileInfo> files = minioService.listFiles(bucketName);
        return Result.success(files);
    }

    /**
     * 列出默认存储桶中的所有文件
     */
    @GetMapping("/files")
    public Result<List<FileInfo>> listFiles() {
        List<FileInfo> files = minioService.listFiles();
        return Result.success(files);
    }
}
