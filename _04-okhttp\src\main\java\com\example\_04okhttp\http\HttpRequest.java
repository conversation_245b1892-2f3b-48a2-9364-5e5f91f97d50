package com.example._04okhttp.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * HTTP请求构建器
 * 支持链式调用，方便构建复杂请求
 */
public class HttpRequest {
    
    private final OkHttpClient client;
    private final String method;
    private final String url;
    private final Map<String, String> headers = new HashMap<>();
    private final Map<String, Object> params = new HashMap<>();
    private Object body;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public HttpRequest(OkHttpClient client, String method, String url) {
        this.client = client;
        this.method = method;
        this.url = url;
    }
    
    /**
     * 添加请求头
     */
    public HttpRequest header(String name, String value) {
        headers.put(name, value);
        return this;
    }
    
    /**
     * 添加多个请求头
     */
    public HttpRequest headers(Map<String, String> headers) {
        this.headers.putAll(headers);
        return this;
    }
    
    /**
     * 添加URL参数
     */
    public HttpRequest param(String name, Object value) {
        params.put(name, value);
        return this;
    }
    
    /**
     * 添加多个URL参数
     */
    public HttpRequest params(Map<String, Object> params) {
        this.params.putAll(params);
        return this;
    }
    
    /**
     * 设置JSON请求体
     */
    public HttpRequest json(Object body) {
        this.body = body;
        header("Content-Type", "application/json");
        return this;
    }
    
    /**
     * 设置表单请求体
     */
    public HttpRequest form(Map<String, String> formData) {
        FormBody.Builder builder = new FormBody.Builder();
        formData.forEach(builder::add);
        this.body = builder.build();
        return this;
    }
    
    /**
     * 执行同步请求
     */
    public HttpResponse execute() {
        try {
            Request request = buildRequest();
            Response response = client.newCall(request).execute();
            return new HttpResponse(response);
        } catch (Exception e) {
            throw new HttpException("请求执行失败", e);
        }
    }
    
    /**
     * 执行异步请求
     */
    public CompletableFuture<HttpResponse> executeAsync() {
        CompletableFuture<HttpResponse> future = new CompletableFuture<>();
        
        Request request = buildRequest();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) {
                future.complete(new HttpResponse(response));
            }
            
            @Override
            public void onFailure(Call call, IOException e) {
                future.completeExceptionally(new HttpException("异步请求失败", e));
            }
        });
        
        return future;
    }
    
    private Request buildRequest() {
        try {
            // 构建URL（包含参数）
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            params.forEach((key, value) -> urlBuilder.addQueryParameter(key, String.valueOf(value)));
            
            // 构建请求体
            RequestBody requestBody = null;
            if (body != null) {
                if (body instanceof RequestBody) {
                    requestBody = (RequestBody) body;
                } else {
                    String json = objectMapper.writeValueAsString(body);
                    requestBody = RequestBody.create(json, MediaType.parse("application/json"));
                }
            }
            
            // 构建请求
            Request.Builder builder = new Request.Builder()
                .url(urlBuilder.build())
                .method(method, requestBody);
            
            // 添加请求头
            headers.forEach(builder::header);
            
            return builder.build();
        } catch (Exception e) {
            throw new HttpException("构建请求失败", e);
        }
    }
}