package com.example._01nacos.controller;

import com.example._01nacos.config.NacosConfig;
import com.example._01nacos.config.OtherGroupConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${server.port}")
    private String port;
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello from " + applicationName + " on port " + port;
    }



    @Autowired
    private NacosConfig nacosConfig;

    @Autowired
    private OtherGroupConfig otherGroupConfig;

    @GetMapping("/get")
    public String getConfig() {
        return "从Nacos获取的配置信息: " + nacosConfig.getConfigInfo();
    }

    @GetMapping("/get-other")
    public String getOtherGroupConfig() {
        return "从Nacos OTHER_GROUP组获取的配置信息: " + otherGroupConfig.getCustomProperty();
    }
} 