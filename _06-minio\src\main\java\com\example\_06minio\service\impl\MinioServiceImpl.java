package com.example._06minio.service.impl;

import com.example._06minio.config.MinioConfig;
import com.example._06minio.entity.FileInfo;
import com.example._06minio.exception.MinioException;
import com.example._06minio.service.MinioService;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * MinIO服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MinioServiceImpl implements MinioService {

    private final MinioClient minioClient;
    private final MinioConfig minioConfig;

    @Override
    public void createBucket(String bucketName) {
        try {
            if (!bucketExists(bucketName)) {
                minioClient.makeBucket(MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build());
                log.info("创建存储桶成功: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("创建存储桶失败: {}", bucketName, e);
            throw new MinioException("创建存储桶失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        try {
            return minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败: {}", bucketName, e);
            throw new MinioException("检查存储桶是否存在失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getAllBuckets() {
        try {
            List<Bucket> buckets = minioClient.listBuckets();
            List<String> bucketNames = new ArrayList<>();
            for (Bucket bucket : buckets) {
                bucketNames.add(bucket.name());
            }
            return bucketNames;
        } catch (Exception e) {
            log.error("获取所有存储桶失败", e);
            throw new MinioException("获取所有存储桶失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void removeBucket(String bucketName) {
        try {
            minioClient.removeBucket(RemoveBucketArgs.builder()
                    .bucket(bucketName)
                    .build());
            log.info("删除存储桶成功: {}", bucketName);
        } catch (Exception e) {
            log.error("删除存储桶失败: {}", bucketName, e);
            throw new MinioException("删除存储桶失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FileInfo uploadFile(MultipartFile file, String bucketName) {
        try {
            return uploadFile(file.getInputStream(), file.getOriginalFilename(), 
                            file.getContentType(), bucketName);
        } catch (Exception e) {
            log.error("上传文件失败: {}", file.getOriginalFilename(), e);
            throw new MinioException("上传文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FileInfo uploadFile(MultipartFile file) {
        return uploadFile(file, minioConfig.getBucketName());
    }

    @Override
    public FileInfo uploadFile(InputStream inputStream, String fileName, String contentType, String bucketName) {
        try {
            // 确保存储桶存在
            createBucket(bucketName);

            // 生成唯一的对象名称
            String extension = FilenameUtils.getExtension(fileName);
            String objectName = UUID.randomUUID().toString();
            if (StringUtils.hasText(extension)) {
                objectName = objectName + "." + extension;
            }

            // 上传文件
            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, -1, 10485760) // 10MB
                    .contentType(contentType)
                    .build());

            // 构建文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(fileName);
            fileInfo.setBucketName(bucketName);
            fileInfo.setObjectName(objectName);
            fileInfo.setContentType(contentType);
            fileInfo.setUploadTime(LocalDateTime.now());
            fileInfo.setFileUrl(getPresignedUrl(bucketName, objectName, 7 * 24 * 3600)); // 7天有效期

            log.info("文件上传成功: {}", fileName);
            return fileInfo;

        } catch (Exception e) {
            log.error("上传文件失败: {}", fileName, e);
            throw new MinioException("上传文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InputStream downloadFile(String bucketName, String objectName) {
        try {
            return minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
        } catch (Exception e) {
            log.error("下载文件失败: {}/{}", bucketName, objectName, e);
            throw new MinioException("下载文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InputStream downloadFile(String objectName) {
        return downloadFile(minioConfig.getBucketName(), objectName);
    }

    @Override
    public void deleteFile(String bucketName, String objectName) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            log.info("删除文件成功: {}/{}", bucketName, objectName);
        } catch (Exception e) {
            log.error("删除文件失败: {}/{}", bucketName, objectName, e);
            throw new MinioException("删除文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteFile(String objectName) {
        deleteFile(minioConfig.getBucketName(), objectName);
    }

    @Override
    public String getPresignedUrl(String bucketName, String objectName, int expires) {
        try {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expires)
                    .build());
        } catch (Exception e) {
            log.error("获取预签名URL失败: {}/{}", bucketName, objectName, e);
            throw new MinioException("获取预签名URL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FileInfo getFileInfo(String bucketName, String objectName) {
        try {
            StatObjectResponse stat = minioClient.statObject(StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());

            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(objectName);
            fileInfo.setBucketName(bucketName);
            fileInfo.setObjectName(objectName);
            fileInfo.setFileSize(stat.size());
            fileInfo.setContentType(stat.contentType());
            fileInfo.setUploadTime(LocalDateTime.ofInstant(stat.lastModified().toInstant(), ZoneId.systemDefault()));
            fileInfo.setFileUrl(getPresignedUrl(bucketName, objectName, 7 * 24 * 3600));

            return fileInfo;
        } catch (Exception e) {
            log.error("获取文件信息失败: {}/{}", bucketName, objectName, e);
            throw new MinioException("获取文件信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FileInfo> listFiles(String bucketName) {
        try {
            List<FileInfo> fileInfos = new ArrayList<>();
            Iterable<Result<Item>> results = minioClient.listObjects(ListObjectsArgs.builder()
                    .bucket(bucketName)
                    .build());

            for (Result<Item> result : results) {
                Item item = result.get();
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFileName(item.objectName());
                fileInfo.setBucketName(bucketName);
                fileInfo.setObjectName(item.objectName());
                fileInfo.setFileSize(item.size());
                fileInfo.setUploadTime(LocalDateTime.ofInstant(item.lastModified().toInstant(), ZoneId.systemDefault()));
                fileInfo.setFileUrl(getPresignedUrl(bucketName, item.objectName(), 7 * 24 * 3600));
                fileInfos.add(fileInfo);
            }

            return fileInfos;
        } catch (Exception e) {
            log.error("列出文件失败: {}", bucketName, e);
            throw new MinioException("列出文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FileInfo> listFiles() {
        return listFiles(minioConfig.getBucketName());
    }
}
