package com.example._02gateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
public class LoginFilter implements GlobalFilter, Ordered {
    
    private final WebClient webClient = WebClient.builder().build();
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        System.out.println("==========进入filter==========");
        String path = exchange.getRequest().getPath().value();
        
        // 白名单路径
        if (path.startsWith("/auth-service/auth/login")) {
            return chain.filter(exchange);
        }
        if (path.startsWith("/auth-service/auth/validate")) {
            return chain.filter(exchange);
        }
        
        // 检查Authorization头
        String authHeader = exchange.getRequest().getHeaders().getFirst("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return unauthorized(exchange);
        }
        
        // 调用auth服务验证token
        return webClient.post()
                .uri("http://localhost:88/auth-service/auth/validate")
                .header("Authorization", authHeader)
                .retrieve()
                .bodyToMono(String.class)
                .flatMap(response -> {
                    if (response.contains("\"valid\":true")) {
                        // 此时放行,转发到目标服务上
                        return chain.filter(exchange);
                    } else {
                        return unauthorized(exchange);
                    }
                })
                .onErrorResume(error -> unauthorized(exchange));
    }
    
    private Mono<Void> unauthorized(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        return response.setComplete();
    }
    
    @Override
    public int getOrder() {
        return -100; // 优先级高
    }
}
