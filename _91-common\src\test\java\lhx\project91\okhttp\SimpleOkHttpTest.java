package lhx.project91.okhttp;

import com.fasterxml.jackson.databind.ObjectMapper;
import lhx.project91.okhttp.model.HttpResult;
import lhx.project91.okhttp.util.OkHttpUtil;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * 简单的OkHttp测试类，不依赖Spring上下文
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
public class SimpleOkHttpTest {

    private OkHttpUtil okHttpUtil;

    @BeforeEach
    public void setUp() {
        // 手动创建OkHttpUtil实例
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .build();
        ObjectMapper objectMapper = new ObjectMapper();
        okHttpUtil = new OkHttpUtil(okHttpClient, objectMapper);
    }

    @Test
    public void testOkHttpUtilCreation() {
        // 测试OkHttpUtil是否能正确创建
        assert okHttpUtil != null : "OkHttpUtil should be created";
        System.out.println("OkHttpUtil创建成功");
    }

    @Test
    public void testHttpResultSuccess() {
        // 测试HttpResult成功结果
        HttpResult<String> result = HttpResult.success("test data");
        
        assert result != null : "Result should not be null";
        assert result.isSuccessful() : "Result should be successful";
        assert result.getStatusCode() == 200 : "Status code should be 200";
        assert "test data".equals(result.getData()) : "Data should match";
        assert "OK".equals(result.getStatusMessage()) : "Status message should be OK";
        
        System.out.println("HttpResult成功测试通过");
    }

    @Test
    public void testHttpResultFailure() {
        // 测试HttpResult失败结果
        HttpResult<String> result = HttpResult.failure(404, "Not Found");
        
        assert result != null : "Result should not be null";
        assert !result.isSuccessful() : "Result should not be successful";
        assert result.getStatusCode() == 404 : "Status code should be 404";
        assert result.getData() == null : "Data should be null";
        assert "Not Found".equals(result.getErrorMessage()) : "Error message should match";
        assert result.isClientError() : "Should be client error";
        
        System.out.println("HttpResult失败测试通过");
    }

    @Test
    public void testHttpResultServerError() {
        // 测试HttpResult服务器错误
        HttpResult<String> result = HttpResult.failure(500, "Internal Server Error");
        
        assert result.isServerError() : "Should be server error";
        assert !result.isClientError() : "Should not be client error";
        
        System.out.println("HttpResult服务器错误测试通过");
    }

    @Test
    public void testHttpResultWithCompleteInfo() {
        // 测试HttpResult完整信息
        HttpResult<String> result = HttpResult.success(201, "Created", "new resource");
        
        assert result.isSuccessful() : "Result should be successful";
        assert result.getStatusCode() == 201 : "Status code should be 201";
        assert "Created".equals(result.getStatusMessage()) : "Status message should be Created";
        assert "new resource".equals(result.getData()) : "Data should match";
        
        System.out.println("HttpResult完整信息测试通过");
    }
}
