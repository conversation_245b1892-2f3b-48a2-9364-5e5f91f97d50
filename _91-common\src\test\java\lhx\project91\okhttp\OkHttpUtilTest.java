package lhx.project91.okhttp;

import lhx.project91.okhttp.model.HttpResult;
import lhx.project91.okhttp.util.OkHttpUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

/**
 * OkHttpUtil 测试类
 *
 * <AUTHOR>
 * @version 2.0.0
 */
@SpringBootTest
@ContextConfiguration(classes = {TestApplication.class, TestConfiguration.class})
public class OkHttpUtilTest {

    @Autowired
    private OkHttpUtil okHttpUtil;

    @Test
    public void testOkHttpUtilNotNull() {
        // 测试OkHttpUtil是否正确注入
        assert okHttpUtil != null : "OkHttpUtil should be injected";
        System.out.println("OkHttpUtil注入成功");
    }

    @Test
    public void testHttpResultCreation() {
        // 测试HttpResult的创建
        HttpResult<String> successResult = HttpResult.success("test data");
        assert successResult.isSuccessful() : "Success result should be successful";
        assert "test data".equals(successResult.getData()) : "Data should match";
        assert successResult.getStatusCode() == 200 : "Status code should be 200";

        HttpResult<String> failureResult = HttpResult.failure(404, "Not Found");
        assert !failureResult.isSuccessful() : "Failure result should not be successful";
        assert failureResult.getStatusCode() == 404 : "Status code should be 404";
        assert "Not Found".equals(failureResult.getErrorMessage()) : "Error message should match";

        System.out.println("HttpResult测试通过");
    }

    // 注释掉需要网络的测试，避免测试依赖外部服务
    /*
    @Test
    public void testGet() {
        try {
            // 测试GET请求 - 需要网络连接
            String result = okHttpUtil.get("https://httpbin.org/get");
            System.out.println("GET请求结果: " + result);
        } catch (Exception e) {
            System.out.println("GET请求失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetForResult() {
        try {
            // 测试GET请求返回HttpResult - 需要网络连接
            HttpResult<String> result = okHttpUtil.getForResult("https://httpbin.org/get");
            System.out.println("GET请求成功: " + result.isSuccessful());
            System.out.println("状态码: " + result.getStatusCode());
            System.out.println("响应数据: " + result.getData());
            System.out.println("耗时: " + result.getDuration() + "ms");
        } catch (Exception e) {
            System.out.println("GET请求失败: " + e.getMessage());
        }
    }
    */
}
