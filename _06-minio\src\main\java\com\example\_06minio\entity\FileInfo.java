package com.example._06minio.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件信息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileInfo {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 对象名称（在MinIO中的路径）
     */
    private String objectName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 文件MD5
     */
    private String md5;
}
