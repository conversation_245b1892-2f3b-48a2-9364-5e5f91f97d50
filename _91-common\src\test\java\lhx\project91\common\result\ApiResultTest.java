package lhx.project91.common.result;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * ApiResult测试类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
public class ApiResultTest {

    @Test
    public void testSuccessResult() {
        // 测试成功结果创建
        ApiResult<String> result1 = ApiResult.success();
        assert result1.isSuccess() : "应该是成功结果";
        assert result1.getCode().equals("1000") : "状态码应该是1000";
        
        ApiResult<String> result2 = ApiResult.success("test data");
        assert result2.isSuccess() : "应该是成功结果";
        assert "test data".equals(result2.getData()) : "数据应该匹配";
        
        System.out.println("成功结果测试通过");
    }

    @Test
    public void testFailureResult() {
        // 测试失败结果创建
        ApiResult<Void> result = ApiResult.failure(ResultCode.NOT_FOUND);
        assert result.isFailure() : "应该是失败结果";
        assert result.getCode().equals("4000") : "状态码应该是4000";
        assert result.getHttpStatus() == 404 : "HTTP状态码应该是404";
        
        System.out.println("失败结果测试通过");
    }

    @Test
    public void testChainedCalls() {
        // 测试链式调用
        ApiResult<String> result = ApiResult.success("data")
                .withTraceId("trace-123")
                .withServer("server-01")
                .withVersion("v1.0");
        
        assert "trace-123".equals(result.getTraceId()) : "追踪ID应该匹配";
        assert "server-01".equals(result.getServer()) : "服务器信息应该匹配";
        assert "v1.0".equals(result.getVersion()) : "版本信息应该匹配";
        
        System.out.println("链式调用测试通过");
    }

    @Test
    public void testPageData() {
        // 测试分页数据
        List<String> records = Arrays.asList("item1", "item2", "item3");
        PageData<String> pageData = PageData.of(records, 1L, 10L, 100L);
        
        assert pageData.getRecords().size() == 3 : "记录数应该是3";
        assert pageData.getCurrent() == 1L : "当前页应该是1";
        assert pageData.getTotal() == 100L : "总数应该是100";
        assert pageData.getPages() == 10L : "总页数应该是10";
        assert pageData.getHasNext() : "应该有下一页";
        assert !pageData.getHasPrevious() : "不应该有上一页";
        
        System.out.println("分页数据测试通过");
    }

    @Test
    public void testApiResultUtils() {
        // 测试工具类方法
        ApiResult<String> result1 = ApiResultUtils.condition(true, "success", ResultCode.BUSINESS_ERROR);
        assert result1.isSuccess() : "条件为真应该返回成功";
        
        ApiResult<String> result2 = ApiResultUtils.condition(false, "success", ResultCode.BUSINESS_ERROR);
        assert result2.isFailure() : "条件为假应该返回失败";
        
        ApiResult<String> result3 = ApiResultUtils.ofNullable("data", ResultCode.NOT_FOUND);
        assert result3.isSuccess() : "非空对象应该返回成功";
        
        ApiResult<String> result4 = ApiResultUtils.ofNullable(null, ResultCode.NOT_FOUND);
        assert result4.isFailure() : "空对象应该返回失败";
        
        System.out.println("工具类测试通过");
    }

    @Test
    public void testBatchResult() {
        // 测试批量操作结果
        BatchResult batchResult = BatchResult.of(100, 95, 5)
                .addFailureDetail("item1", "处理失败")
                .addFailureDetail("item2", "数据错误");
        
        assert batchResult.getTotalCount() == 100 : "总数应该是100";
        assert batchResult.getSuccessCount() == 95 : "成功数应该是95";
        assert batchResult.getFailureCount() == 5 : "失败数应该是5";
        assert batchResult.getSuccessRate() == 95.0 : "成功率应该是95%";
        assert batchResult.isPartialSuccess() : "应该是部分成功";
        assert batchResult.hasFailureDetails() : "应该有失败详情";
        
        System.out.println("批量结果测试通过");
        System.out.println("批量操作摘要: " + batchResult.getSummary());
    }

    @Test
    public void testBusinessException() {
        // 测试业务异常
        try {
            throw BusinessException.notFound("用户不存在");
        } catch (BusinessException e) {
            assert e.getResultCode() == ResultCode.NOT_FOUND : "结果码应该是NOT_FOUND";
            assert "用户不存在".equals(e.getMessage()) : "消息应该匹配";
            System.out.println("业务异常测试通过");
        }
        
        // 测试断言方法
        try {
            BusinessException.assertTrue(false, ResultCode.PARAM_ERROR, "参数错误");
            assert false : "应该抛出异常";
        } catch (BusinessException e) {
            assert e.getResultCode() == ResultCode.PARAM_ERROR : "结果码应该是PARAM_ERROR";
            System.out.println("断言方法测试通过");
        }
    }

    @Test
    public void testResultCode() {
        // 测试结果码枚举
        ResultCode success = ResultCode.SUCCESS;
        assert success.isSuccess() : "SUCCESS应该是成功状态";
        assert success.getHttpStatus() == 200 : "HTTP状态码应该是200";
        
        ResultCode notFound = ResultCode.NOT_FOUND;
        assert !notFound.isSuccess() : "NOT_FOUND不应该是成功状态";
        assert notFound.isClientError() : "NOT_FOUND应该是客户端错误";
        
        ResultCode systemError = ResultCode.SYSTEM_ERROR;
        assert systemError.isServerError() : "SYSTEM_ERROR应该是服务器错误";
        
        // 测试根据代码查找
        ResultCode found = ResultCode.getByCode("1000");
        assert found == ResultCode.SUCCESS : "应该找到SUCCESS";
        
        System.out.println("结果码测试通过");
    }
}
