package lhx.project91.common.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一API响应结果封装类
 * 
 * <p>这是一个企业级的响应结果封装类，提供了完整的API响应功能：</p>
 * <ul>
 *   <li>支持泛型数据封装</li>
 *   <li>标准HTTP状态码和业务状态码</li>
 *   <li>国际化消息支持</li>
 *   <li>请求追踪和时间戳</li>
 *   <li>链式调用支持</li>
 *   <li>分页数据封装</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>{@code
 * // 成功响应
 * ApiResult<User> result = ApiResult.success(user);
 * 
 * // 失败响应
 * ApiResult<Void> result = ApiResult.failure(ResultCode.USER_NOT_FOUND);
 * 
 * // 分页响应
 * ApiResult<PageData<User>> result = ApiResult.success(pageData);
 * 
 * // 链式调用
 * ApiResult<String> result = ApiResult.<String>builder()
 *     .success(true)
 *     .code(200)
 *     .message("操作成功")
 *     .data("result data")
 *     .traceId("trace-123")
 *     .build();
 * }</pre>
 * 
 * @param <T> 响应数据的类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求是否成功
     */
    private Boolean success;

    /**
     * HTTP状态码
     */
    private Integer httpStatus;

    /**
     * 业务状态码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 错误详情（仅在失败时返回）
     */
    private String errorDetail;

    /**
     * 请求追踪ID
     */
    private String traceId;

    /**
     * 响应时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /**
     * 服务器信息（可选）
     */
    private String server;

    /**
     * API版本（可选）
     */
    private String version;

    // ==================== 成功响应静态工厂方法 ====================

    /**
     * 创建成功响应（无数据）
     * 
     * @param <T> 数据类型
     * @return 成功的ApiResult
     */
    public static <T> ApiResult<T> success() {
        return ApiResult.<T>builder()
                .success(true)
                .httpStatus(200)
                .code(ResultCode.SUCCESS.getCode())
                .message(ResultCode.SUCCESS.getMessage())
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应（带数据）
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功的ApiResult
     */
    public static <T> ApiResult<T> success(T data) {
        return ApiResult.<T>builder()
                .success(true)
                .httpStatus(200)
                .code(ResultCode.SUCCESS.getCode())
                .message(ResultCode.SUCCESS.getMessage())
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应（带数据和消息）
     * 
     * @param data 响应数据
     * @param message 自定义消息
     * @param <T> 数据类型
     * @return 成功的ApiResult
     */
    public static <T> ApiResult<T> success(T data, String message) {
        return ApiResult.<T>builder()
                .success(true)
                .httpStatus(200)
                .code(ResultCode.SUCCESS.getCode())
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应（完整参数）
     * 
     * @param data 响应数据
     * @param message 自定义消息
     * @param traceId 追踪ID
     * @param <T> 数据类型
     * @return 成功的ApiResult
     */
    public static <T> ApiResult<T> success(T data, String message, String traceId) {
        return ApiResult.<T>builder()
                .success(true)
                .httpStatus(200)
                .code(ResultCode.SUCCESS.getCode())
                .message(message)
                .data(data)
                .traceId(traceId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    // ==================== 失败响应静态工厂方法 ====================

    /**
     * 创建失败响应（使用ResultCode）
     * 
     * @param resultCode 结果码枚举
     * @param <T> 数据类型
     * @return 失败的ApiResult
     */
    public static <T> ApiResult<T> failure(ResultCode resultCode) {
        return ApiResult.<T>builder()
                .success(false)
                .httpStatus(resultCode.getHttpStatus())
                .code(resultCode.getCode())
                .message(resultCode.getMessage())
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应（使用ResultCode和自定义消息）
     * 
     * @param resultCode 结果码枚举
     * @param message 自定义消息
     * @param <T> 数据类型
     * @return 失败的ApiResult
     */
    public static <T> ApiResult<T> failure(ResultCode resultCode, String message) {
        return ApiResult.<T>builder()
                .success(false)
                .httpStatus(resultCode.getHttpStatus())
                .code(resultCode.getCode())
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应（使用ResultCode、消息和错误详情）
     * 
     * @param resultCode 结果码枚举
     * @param message 自定义消息
     * @param errorDetail 错误详情
     * @param <T> 数据类型
     * @return 失败的ApiResult
     */
    public static <T> ApiResult<T> failure(ResultCode resultCode, String message, String errorDetail) {
        return ApiResult.<T>builder()
                .success(false)
                .httpStatus(resultCode.getHttpStatus())
                .code(resultCode.getCode())
                .message(message)
                .errorDetail(errorDetail)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应（完整参数）
     * 
     * @param resultCode 结果码枚举
     * @param message 自定义消息
     * @param errorDetail 错误详情
     * @param traceId 追踪ID
     * @param <T> 数据类型
     * @return 失败的ApiResult
     */
    public static <T> ApiResult<T> failure(ResultCode resultCode, String message, String errorDetail, String traceId) {
        return ApiResult.<T>builder()
                .success(false)
                .httpStatus(resultCode.getHttpStatus())
                .code(resultCode.getCode())
                .message(message)
                .errorDetail(errorDetail)
                .traceId(traceId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 判断是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }

    /**
     * 判断是否失败
     * 
     * @return true表示失败，false表示成功
     */
    public boolean isFailure() {
        return !isSuccess();
    }

    /**
     * 判断是否为客户端错误（4xx）
     * 
     * @return true表示客户端错误
     */
    public boolean isClientError() {
        return httpStatus != null && httpStatus >= 400 && httpStatus < 500;
    }

    /**
     * 判断是否为服务器错误（5xx）
     * 
     * @return true表示服务器错误
     */
    public boolean isServerError() {
        return httpStatus != null && httpStatus >= 500;
    }

    // ==================== 链式调用支持 ====================

    /**
     * 设置追踪ID（链式调用）
     * 
     * @param traceId 追踪ID
     * @return 当前ApiResult实例
     */
    public ApiResult<T> withTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }

    /**
     * 设置服务器信息（链式调用）
     * 
     * @param server 服务器信息
     * @return 当前ApiResult实例
     */
    public ApiResult<T> withServer(String server) {
        this.server = server;
        return this;
    }

    /**
     * 设置API版本（链式调用）
     * 
     * @param version API版本
     * @return 当前ApiResult实例
     */
    public ApiResult<T> withVersion(String version) {
        this.version = version;
        return this;
    }

    /**
     * 设置错误详情（链式调用）
     * 
     * @param errorDetail 错误详情
     * @return 当前ApiResult实例
     */
    public ApiResult<T> withErrorDetail(String errorDetail) {
        this.errorDetail = errorDetail;
        return this;
    }
}
