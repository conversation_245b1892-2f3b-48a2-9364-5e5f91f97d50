package lhx.project91.okhttp.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * HTTP请求结果封装类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HttpResult<T> {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * HTTP状态码
     */
    private int statusCode;
    
    /**
     * 状态消息
     */
    private String statusMessage;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 响应头信息
     */
    private Map<String, String> headers;
    
    /**
     * 请求URL
     */
    private String url;
    
    /**
     * 请求方法
     */
    private String method;
    
    /**
     * 请求耗时（毫秒）
     */
    private long duration;
    
    /**
     * 请求时间
     */
    private LocalDateTime requestTime;
    
    public HttpResult(boolean success, int statusCode, T data) {
        this.success = success;
        this.statusCode = statusCode;
        this.data = data;
        this.requestTime = LocalDateTime.now();
    }
    
    public HttpResult(boolean success, int statusCode, T data, String errorMessage) {
        this.success = success;
        this.statusCode = statusCode;
        this.data = data;
        this.errorMessage = errorMessage;
        this.requestTime = LocalDateTime.now();
    }

    /**
     * 成功结果
     */
    public static <T> HttpResult<T> success(T data) {
        HttpResult<T> result = new HttpResult<>(true, 200, data);
        result.setStatusMessage("OK");
        return result;
    }
    
    /**
     * 成功结果（带状态码）
     */
    public static <T> HttpResult<T> success(int statusCode, T data) {
        HttpResult<T> result = new HttpResult<>(true, statusCode, data);
        result.setStatusMessage("OK");
        return result;
    }
    
    /**
     * 成功结果（带完整信息）
     */
    public static <T> HttpResult<T> success(int statusCode, String statusMessage, T data) {
        HttpResult<T> result = new HttpResult<>(true, statusCode, data);
        result.setStatusMessage(statusMessage);
        return result;
    }
    
    /**
     * 失败结果
     */
    public static <T> HttpResult<T> failure(int statusCode, String errorMessage) {
        HttpResult<T> result = new HttpResult<>(false, statusCode, null, errorMessage);
        result.setStatusMessage("Error");
        return result;
    }
    
    /**
     * 失败结果（默认500状态码）
     */
    public static <T> HttpResult<T> failure(String errorMessage) {
        HttpResult<T> result = new HttpResult<>(false, 500, null, errorMessage);
        result.setStatusMessage("Internal Server Error");
        return result;
    }
    
    /**
     * 失败结果（带完整信息）
     */
    public static <T> HttpResult<T> failure(int statusCode, String statusMessage, String errorMessage) {
        HttpResult<T> result = new HttpResult<>(false, statusCode, null, errorMessage);
        result.setStatusMessage(statusMessage);
        return result;
    }
    
    /**
     * 判断是否为成功的HTTP状态码
     */
    public boolean isSuccessful() {
        return success && statusCode >= 200 && statusCode < 300;
    }
    
    /**
     * 判断是否为客户端错误
     */
    public boolean isClientError() {
        return statusCode >= 400 && statusCode < 500;
    }
    
    /**
     * 判断是否为服务器错误
     */
    public boolean isServerError() {
        return statusCode >= 500;
    }
}
