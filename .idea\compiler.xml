<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="helloworld" />
        <module name="helloworld (1)" />
        <module name="helloworld (2)" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="helloworld" options="-parameters" />
      <module name="helloworld (1)" options="-parameters" />
      <module name="helloworld (2)" options="-parameters" />
    </option>
  </component>
</project>