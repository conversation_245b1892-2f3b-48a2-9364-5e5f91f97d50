# Nacos OTHER_GROUP组配置指南

## 在Nacos中添加OTHER_GROUP组的配置
1. 登录Nacos控制台（默认地址：http://127.0.0.1:8848/nacos）
2. 点击左侧菜单栏中的"配置管理" -> "配置列表"
3. 点击右上角的"+"按钮，添加配置
4. 填写配置信息：
   - Data ID: `other-config.yaml`（与bootstrap.yml中配置的shared-configs一致）
   - Group: `OTHER_GROUP`（注意大小写必须一致）
   - 配置格式：选择`YAML`
   - 配置内容：
     ```yaml
     custom:
       property: "这是从Nacos OTHER_GROUP组加载的配置"
     ```
5. 点击"发布"按钮保存配置

## 测试OTHER_GROUP组配置读取
1. 启动应用程序
2. 访问：`http://localhost:8080/config/get-other`
3. 应该能看到从Nacos OTHER_GROUP组获取的配置信息
4. 在Nacos控制台修改配置值，几秒钟后再次访问接口，可以观察到配置已经动态刷新

## 技术说明
1. 通过在bootstrap.yml中配置`shared-configs`来引入其他组或其他DataID的配置
2. `refresh: true`表示配置更新后会自动刷新到应用程序
3. 可以引入多个共享配置，按照数组顺序加载，后加载的优先级更高 