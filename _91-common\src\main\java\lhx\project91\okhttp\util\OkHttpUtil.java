package lhx.project91.okhttp.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lhx.project91.okhttp.exception.OkHttpException;
import lhx.project91.okhttp.model.HttpResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * OkHttp工具类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Slf4j
@Component
public class OkHttpUtil {
    
    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public OkHttpUtil(OkHttpClient okHttpClient, ObjectMapper objectMapper) {
        this.okHttpClient = okHttpClient;
        this.objectMapper = objectMapper;
    }

    // ==================== 同步请求方法 ====================
    
    /**
     * GET请求 - 返回字符串
     * 
     * @param url 请求URL
     * @return 响应字符串
     */
    public String get(String url) {
        return get(url, null);
    }
    
    /**
     * GET请求 - 返回字符串
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应字符串
     */
    public String get(String url, Map<String, String> headers) {
        HttpResult<String> result = getForResult(url, headers);
        if (result.isSuccessful()) {
            return result.getData();
        }
        throw new OkHttpException(result.getStatusCode(), result.getErrorMessage(), url, "GET");
    }
    
    /**
     * GET请求 - 返回HttpResult
     * 
     * @param url 请求URL
     * @return HttpResult包装的响应
     */
    public HttpResult<String> getForResult(String url) {
        return getForResult(url, null);
    }
    
    /**
     * GET请求 - 返回HttpResult
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return HttpResult包装的响应
     */
    public HttpResult<String> getForResult(String url, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder().url(url);
        
        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        
        Request request = builder.build();
        return executeRequest(request);
    }
    
    /**
     * GET请求 - 返回指定类型对象
     * 
     * @param url 请求URL
     * @param clazz 返回类型
     * @return 指定类型对象
     */
    public <T> T getForObject(String url, Class<T> clazz) {
        return getForObject(url, null, clazz);
    }
    
    /**
     * GET请求 - 返回指定类型对象
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param clazz 返回类型
     * @return 指定类型对象
     */
    public <T> T getForObject(String url, Map<String, String> headers, Class<T> clazz) {
        HttpResult<String> result = getForResult(url, headers);
        if (result.isSuccessful()) {
            return parseJson(result.getData(), clazz);
        }
        throw new OkHttpException(result.getStatusCode(), result.getErrorMessage(), url, "GET");
    }
    
    /**
     * GET请求 - 返回指定类型对象的HttpResult
     * 
     * @param url 请求URL
     * @param clazz 返回类型
     * @return HttpResult包装的指定类型对象
     */
    public <T> HttpResult<T> getForObjectResult(String url, Class<T> clazz) {
        return getForObjectResult(url, null, clazz);
    }
    
    /**
     * GET请求 - 返回指定类型对象的HttpResult
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param clazz 返回类型
     * @return HttpResult包装的指定类型对象
     */
    public <T> HttpResult<T> getForObjectResult(String url, Map<String, String> headers, Class<T> clazz) {
        HttpResult<String> stringResult = getForResult(url, headers);
        if (stringResult.isSuccessful()) {
            try {
                T data = parseJson(stringResult.getData(), clazz);
                return HttpResult.success(stringResult.getStatusCode(), stringResult.getStatusMessage(), data);
            } catch (Exception e) {
                return HttpResult.failure(500, "JSON解析失败: " + e.getMessage());
            }
        }
        return HttpResult.failure(stringResult.getStatusCode(), stringResult.getStatusMessage(), stringResult.getErrorMessage());
    }
    
    // ==================== POST 请求方法 ====================
    
    /**
     * POST请求 - JSON格式
     * 
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @return 响应字符串
     */
    public String postJson(String url, String jsonBody) {
        return postJson(url, jsonBody, null);
    }
    
    /**
     * POST请求 - JSON格式
     * 
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return 响应字符串
     */
    public String postJson(String url, String jsonBody, Map<String, String> headers) {
        HttpResult<String> result = postJsonForResult(url, jsonBody, headers);
        if (result.isSuccessful()) {
            return result.getData();
        }
        throw new OkHttpException(result.getStatusCode(), result.getErrorMessage(), url, "POST");
    }
    
    /**
     * POST请求 - JSON格式，返回HttpResult
     * 
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @return HttpResult包装的响应
     */
    public HttpResult<String> postJsonForResult(String url, String jsonBody) {
        return postJsonForResult(url, jsonBody, null);
    }
    
    /**
     * POST请求 - JSON格式，返回HttpResult
     * 
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return HttpResult包装的响应
     */
    public HttpResult<String> postJsonForResult(String url, String jsonBody, Map<String, String> headers) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);
        
        Request.Builder builder = new Request.Builder()
                .url(url)
                .post(body);
        
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        
        Request request = builder.build();
        return executeRequest(request);
    }
    
    /**
     * POST请求 - 对象转JSON
     * 
     * @param url 请求URL
     * @param object 请求对象
     * @return 响应字符串
     */
    public String postObject(String url, Object object) {
        return postObject(url, object, null);
    }
    
    /**
     * POST请求 - 对象转JSON
     * 
     * @param url 请求URL
     * @param object 请求对象
     * @param headers 请求头
     * @return 响应字符串
     */
    public String postObject(String url, Object object, Map<String, String> headers) {
        try {
            String jsonBody = objectMapper.writeValueAsString(object);
            return postJson(url, jsonBody, headers);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON失败", e);
            throw new OkHttpException("对象转JSON失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * POST请求 - 对象转JSON，返回指定类型对象
     * 
     * @param url 请求URL
     * @param object 请求对象
     * @param responseClass 响应类型
     * @return 指定类型对象
     */
    public <T> T postForObject(String url, Object object, Class<T> responseClass) {
        return postForObject(url, object, null, responseClass);
    }
    
    /**
     * POST请求 - 对象转JSON，返回指定类型对象
     * 
     * @param url 请求URL
     * @param object 请求对象
     * @param headers 请求头
     * @param responseClass 响应类型
     * @return 指定类型对象
     */
    public <T> T postForObject(String url, Object object, Map<String, String> headers, Class<T> responseClass) {
        String response = postObject(url, object, headers);
        return parseJson(response, responseClass);
    }

    // ==================== 表单请求方法 ====================

    /**
     * POST请求 - 表单格式
     *
     * @param url 请求URL
     * @param formData 表单数据
     * @return 响应字符串
     */
    public String postForm(String url, Map<String, String> formData) {
        return postForm(url, formData, null);
    }

    /**
     * POST请求 - 表单格式
     *
     * @param url 请求URL
     * @param formData 表单数据
     * @param headers 请求头
     * @return 响应字符串
     */
    public String postForm(String url, Map<String, String> formData, Map<String, String> headers) {
        HttpResult<String> result = postFormForResult(url, formData, headers);
        if (result.isSuccessful()) {
            return result.getData();
        }
        throw new OkHttpException(result.getStatusCode(), result.getErrorMessage(), url, "POST");
    }

    /**
     * POST请求 - 表单格式，返回HttpResult
     *
     * @param url 请求URL
     * @param formData 表单数据
     * @param headers 请求头
     * @return HttpResult包装的响应
     */
    public HttpResult<String> postFormForResult(String url, Map<String, String> formData, Map<String, String> headers) {
        FormBody.Builder formBuilder = new FormBody.Builder();

        if (formData != null && !formData.isEmpty()) {
            formData.forEach(formBuilder::add);
        }

        RequestBody body = formBuilder.build();

        Request.Builder builder = new Request.Builder()
                .url(url)
                .post(body);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }

        Request request = builder.build();
        return executeRequest(request);
    }

    // ==================== PUT/DELETE 请求方法 ====================

    /**
     * PUT请求 - JSON格式
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @return 响应字符串
     */
    public String putJson(String url, String jsonBody) {
        return putJson(url, jsonBody, null);
    }

    /**
     * PUT请求 - JSON格式
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return 响应字符串
     */
    public String putJson(String url, String jsonBody, Map<String, String> headers) {
        HttpResult<String> result = putJsonForResult(url, jsonBody, headers);
        if (result.isSuccessful()) {
            return result.getData();
        }
        throw new OkHttpException(result.getStatusCode(), result.getErrorMessage(), url, "PUT");
    }

    /**
     * PUT请求 - JSON格式，返回HttpResult
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return HttpResult包装的响应
     */
    public HttpResult<String> putJsonForResult(String url, String jsonBody, Map<String, String> headers) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        Request.Builder builder = new Request.Builder()
                .url(url)
                .put(body);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }

        Request request = builder.build();
        return executeRequest(request);
    }

    /**
     * DELETE请求
     *
     * @param url 请求URL
     * @return 响应字符串
     */
    public String delete(String url) {
        return delete(url, null);
    }

    /**
     * DELETE请求
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应字符串
     */
    public String delete(String url, Map<String, String> headers) {
        HttpResult<String> result = deleteForResult(url, headers);
        if (result.isSuccessful()) {
            return result.getData();
        }
        throw new OkHttpException(result.getStatusCode(), result.getErrorMessage(), url, "DELETE");
    }

    /**
     * DELETE请求 - 返回HttpResult
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return HttpResult包装的响应
     */
    public HttpResult<String> deleteForResult(String url, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder()
                .url(url)
                .delete();

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }

        Request request = builder.build();
        return executeRequest(request);
    }

    // ==================== 核心执行方法 ====================

    /**
     * 执行请求 - 返回HttpResult
     *
     * @param request 请求对象
     * @return HttpResult包装的响应
     */
    private HttpResult<String> executeRequest(Request request) {
        long startTime = System.currentTimeMillis();

        try (Response response = okHttpClient.newCall(request).execute()) {
            long duration = System.currentTimeMillis() - startTime;

            String responseBody = "";
            if (response.body() != null) {
                responseBody = response.body().string();
            }

            // 构建响应头Map
            Map<String, String> headerMap = new HashMap<>();
            response.headers().forEach(pair -> headerMap.put(pair.getFirst(), pair.getSecond()));

            HttpResult<String> result = new HttpResult<>();
            result.setSuccess(response.isSuccessful());
            result.setStatusCode(response.code());
            result.setStatusMessage(response.message());
            result.setData(responseBody);
            result.setHeaders(headerMap);
            result.setUrl(request.url().toString());
            result.setMethod(request.method());
            result.setDuration(duration);
            result.setRequestTime(LocalDateTime.now());

            if (!response.isSuccessful()) {
                result.setErrorMessage("HTTP " + response.code() + ": " + response.message());
            }

            log.debug("请求URL: {}, 方法: {}, 响应状态: {}, 耗时: {}ms",
                     request.url(), request.method(), response.code(), duration);

            return result;

        } catch (IOException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("HTTP请求执行失败: {} {}", request.method(), request.url(), e);

            HttpResult<String> result = new HttpResult<>();
            result.setSuccess(false);
            result.setStatusCode(-1);
            result.setStatusMessage("Network Error");
            result.setErrorMessage("网络请求失败: " + e.getMessage());
            result.setUrl(request.url().toString());
            result.setMethod(request.method());
            result.setDuration(duration);
            result.setRequestTime(LocalDateTime.now());

            return result;
        }
    }

    /**
     * JSON解析方法
     *
     * @param json JSON字符串
     * @param clazz 目标类型
     * @return 解析后的对象
     */
    private <T> T parseJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON解析失败: {}", json, e);
            throw new OkHttpException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * JSON解析方法 - 支持泛型
     *
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @return 解析后的对象
     */
    private <T> T parseJson(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            log.error("JSON解析失败: {}", json, e);
            throw new OkHttpException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    // ==================== 异步请求方法 ====================

    /**
     * 异步GET请求 - 返回CompletableFuture
     *
     * @param url 请求URL
     * @return CompletableFuture包装的HttpResult
     */
    public CompletableFuture<HttpResult<String>> getAsyncFuture(String url) {
        return getAsyncFuture(url, null);
    }

    /**
     * 异步GET请求 - 返回CompletableFuture
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return CompletableFuture包装的HttpResult
     */
    public CompletableFuture<HttpResult<String>> getAsyncFuture(String url, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder().url(url);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }

        Request request = builder.build();
        return executeRequestAsync(request);
    }

    /**
     * 异步POST请求 - 返回CompletableFuture
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @return CompletableFuture包装的HttpResult
     */
    public CompletableFuture<HttpResult<String>> postJsonAsyncFuture(String url, String jsonBody) {
        return postJsonAsyncFuture(url, jsonBody, null);
    }

    /**
     * 异步POST请求 - 返回CompletableFuture
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return CompletableFuture包装的HttpResult
     */
    public CompletableFuture<HttpResult<String>> postJsonAsyncFuture(String url, String jsonBody, Map<String, String> headers) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        Request.Builder builder = new Request.Builder()
                .url(url)
                .post(body);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }

        Request request = builder.build();
        return executeRequestAsync(request);
    }

    /**
     * 异步执行请求
     *
     * @param request 请求对象
     * @return CompletableFuture包装的HttpResult
     */
    private CompletableFuture<HttpResult<String>> executeRequestAsync(Request request) {
        CompletableFuture<HttpResult<String>> future = new CompletableFuture<>();

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                HttpResult<String> result = new HttpResult<>();
                result.setSuccess(false);
                result.setStatusCode(-1);
                result.setStatusMessage("Network Error");
                result.setErrorMessage("网络请求失败: " + e.getMessage());
                result.setUrl(request.url().toString());
                result.setMethod(request.method());
                result.setRequestTime(LocalDateTime.now());

                future.complete(result);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = "";
                    if (response.body() != null) {
                        responseBody = response.body().string();
                    }

                    Map<String, String> headerMap = new HashMap<>();
                    response.headers().forEach(pair -> headerMap.put(pair.getFirst(), pair.getSecond()));

                    HttpResult<String> result = new HttpResult<>();
                    result.setSuccess(response.isSuccessful());
                    result.setStatusCode(response.code());
                    result.setStatusMessage(response.message());
                    result.setData(responseBody);
                    result.setHeaders(headerMap);
                    result.setUrl(request.url().toString());
                    result.setMethod(request.method());
                    result.setRequestTime(LocalDateTime.now());

                    if (!response.isSuccessful()) {
                        result.setErrorMessage("HTTP " + response.code() + ": " + response.message());
                    }

                    future.complete(result);
                } finally {
                    response.close();
                }
            }
        });

        return future;
    }

    // ==================== 传统回调方式异步请求 ====================

    /**
     * 异步GET请求 - 传统回调方式
     *
     * @param url 请求URL
     * @param callback 回调函数
     */
    public void getAsync(String url, Callback callback) {
        Request request = new Request.Builder().url(url).build();
        okHttpClient.newCall(request).enqueue(callback);
    }

    /**
     * 异步POST请求 - 传统回调方式
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param callback 回调函数
     */
    public void postJsonAsync(String url, String jsonBody, Callback callback) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        okHttpClient.newCall(request).enqueue(callback);
    }
}
