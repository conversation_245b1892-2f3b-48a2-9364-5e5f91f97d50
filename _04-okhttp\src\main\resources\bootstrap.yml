spring:
  application:
    name: okhttp-service
  cloud:
    nacos:
      discovery:
        # 服务发现
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631  # 服务发现命名空间
      config:
        # 配置中心
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631 # 配置中心命名空间
        file-extension: yaml
        shared-configs:
          - data-id: other-config.yaml
            group: OTHER_GROUP
            refresh: true
# HTTP客户端配置
http:
  client:
    connect-timeout: 10
    read-timeout: 30
    write-timeout: 30
    max-retries: 3
    enable-logging: true
    max-idle-connections: 5
    keep-alive-duration: 5

# 日志配置
logging:
  level:
    com.example._04okhttp: DEBUG
    okhttp3: DEBUG