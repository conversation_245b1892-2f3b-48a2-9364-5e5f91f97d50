package com.example._04okhttp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * HTTP客户端配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "http.client")
public class HttpConfig {
    
    /**
     * 连接超时时间（秒）
     */
    private int connectTimeout = 10;
    
    /**
     * 读取超时时间（秒）
     */
    private int readTimeout = 30;
    
    /**
     * 写入超时时间（秒）
     */
    private int writeTimeout = 30;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 是否启用日志
     */
    private boolean enableLogging = true;
    
    /**
     * 连接池最大空闲连接数
     */
    private int maxIdleConnections = 5;
    
    /**
     * 连接存活时间（分钟）
     */
    private int keepAliveDuration = 5;
}