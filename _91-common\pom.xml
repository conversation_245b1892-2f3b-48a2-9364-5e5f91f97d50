<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <!--        <version>2.4.5</version>-->
        <version>2.7.9</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>lhx.91project</groupId>
    <artifactId>okhttp-spring-boot-starter</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <name>okhttp-spring-boot-starter</name>
    <description>OkHttp Spring Boot Starter</description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!-- Spring Boot 自动配置 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- Spring Boot 配置处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Boot Web Starter (提供Web相关功能) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Boot Validation (参数校验支持) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- OkHttp依赖 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>

        <!-- Jackson JSON处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Jackson 核心 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <!-- Jackson 注解 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Boot Test (测试用) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- SLF4J 日志 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>



    <build>
        <plugins>
            <!-- 只上传普通 jar + 源码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals><goal>jar</goal></goals>
                    </execution>
                </executions>
            </plugin>

<!--            &lt;!&ndash; Maven 编译插件 &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <version>3.11.0</version>-->
<!--                <configuration>-->
<!--                    <source>17</source>-->
<!--                    <target>17</target>-->
<!--                    <encoding>UTF-8</encoding>-->
<!--                </configuration>-->
<!--            </plugin>-->

<!--            &lt;!&ndash; Maven 源码插件 &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-source-plugin</artifactId>-->
<!--                <version>3.3.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>attach-sources</id>-->
<!--                        <goals>-->
<!--                            <goal>jar</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

<!--            &lt;!&ndash; Maven Surefire 测试插件 &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->
<!--                <version>3.1.2</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash; 跳过需要网络连接的测试 &ndash;&gt;-->
<!--                    <excludes>-->
<!--                        <exclude>**/*NetworkTest.java</exclude>-->
<!--                    </excludes>-->
<!--                    &lt;!&ndash; 如果测试失败不中断构建 &ndash;&gt;-->
<!--                    <testFailureIgnore>false</testFailureIgnore>-->
<!--                    &lt;!&ndash; 设置测试超时 &ndash;&gt;-->
<!--                    <forkedProcessTimeoutInSeconds>60</forkedProcessTimeoutInSeconds>-->
<!--                </configuration>-->
<!--            </plugin>-->

        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://localhost:666/repository/maven-releases/</url>
        </repository>

        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://localhost:666/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>

