# OkHttp Spring Boot Starter 使用指南

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>lhx.91project</groupId>
    <artifactId>okhttp-spring-boot-starter</artifactId>
    <version>2.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 基本使用

```java
@RestController
public class ApiController {
    
    @Autowired
    private OkHttpUtil okHttpUtil;
    
    @GetMapping("/test")
    public String test() {
        return okHttpUtil.get("https://httpbin.org/get");
    }
}
```

## 详细使用示例

### GET 请求

```java
// 简单GET请求
String response = okHttpUtil.get("https://api.example.com/users");

// 带请求头的GET请求
Map<String, String> headers = new HashMap<>();
headers.put("Authorization", "Bearer your-token");
String response = okHttpUtil.get("https://api.example.com/users", headers);

// 获取HttpResult（包含状态码、耗时等信息）
HttpResult<String> result = okHttpUtil.getForResult("https://api.example.com/users");
if (result.isSuccessful()) {
    System.out.println("响应: " + result.getData());
    System.out.println("状态码: " + result.getStatusCode());
    System.out.println("耗时: " + result.getDuration() + "ms");
}

// 直接获取对象
User user = okHttpUtil.getForObject("https://api.example.com/users/1", User.class);
```

### POST 请求

```java
// POST JSON字符串
String jsonBody = "{\"name\":\"张三\",\"age\":25}";
String response = okHttpUtil.postJson("https://api.example.com/users", jsonBody);

// POST 对象（自动转JSON）
User user = new User("张三", 25);
String response = okHttpUtil.postObject("https://api.example.com/users", user);

// POST 表单
Map<String, String> formData = new HashMap<>();
formData.put("username", "admin");
formData.put("password", "123456");
String response = okHttpUtil.postForm("https://api.example.com/login", formData);

// POST 并获取对象响应
User newUser = okHttpUtil.postForObject("https://api.example.com/users", user, User.class);
```

### PUT/DELETE 请求

```java
// PUT 请求
String jsonBody = "{\"name\":\"李四\",\"age\":30}";
String response = okHttpUtil.putJson("https://api.example.com/users/1", jsonBody);

// DELETE 请求
String response = okHttpUtil.delete("https://api.example.com/users/1");
```

### 异步请求

```java
// 异步GET请求
CompletableFuture<HttpResult<String>> future = okHttpUtil.getAsyncFuture("https://api.example.com/users");
future.thenAccept(result -> {
    if (result.isSuccessful()) {
        System.out.println("异步请求成功: " + result.getData());
    }
}).exceptionally(throwable -> {
    System.err.println("异步请求失败: " + throwable.getMessage());
    return null;
});

// 异步POST请求
CompletableFuture<HttpResult<String>> future = okHttpUtil.postJsonAsyncFuture(
    "https://api.example.com/users", jsonBody);
```

### 使用 OkHttpTemplate

```java
@Service
public class UserService {
    
    @Autowired
    private OkHttpTemplate okHttpTemplate;
    
    public HttpResult<List<User>> getAllUsers() {
        return okHttpTemplate.getForObject("https://api.example.com/users", 
            new TypeReference<List<User>>() {});
    }
    
    public HttpResult<User> createUser(User user) {
        return okHttpTemplate.postForObject("https://api.example.com/users", 
            user, User.class);
    }
    
    public HttpResult<String> deleteUser(Long id) {
        return okHttpTemplate.deleteForString("https://api.example.com/users/" + id);
    }
}
```

## 异常处理

```java
try {
    String response = okHttpUtil.get("https://api.example.com/users");
    // 处理响应
} catch (OkHttpException e) {
    System.err.println("HTTP请求失败:");
    System.err.println("状态码: " + e.getStatusCode());
    System.err.println("URL: " + e.getUrl());
    System.err.println("方法: " + e.getMethod());
    System.err.println("错误信息: " + e.getMessage());
}

// 或者使用HttpResult方式（推荐）
HttpResult<String> result = okHttpUtil.getForResult("https://api.example.com/users");
if (result.isSuccessful()) {
    // 处理成功响应
    String data = result.getData();
} else {
    // 处理失败响应
    System.err.println("请求失败: " + result.getErrorMessage());
    System.err.println("状态码: " + result.getStatusCode());
}
```

## 配置示例

```yaml
okhttp:
  enabled: true
  connect-timeout: 10s
  read-timeout: 30s
  write-timeout: 30s
  call-timeout: 60s
  connection-pool:
    max-idle-connections: 5
    keep-alive-duration: 5m
  retry:
    enabled: true
    max-retries: 3
    retry-interval: 1s
  logging:
    enabled: true
    level: BASIC
```

## 最佳实践

1. **使用HttpResult**: 推荐使用返回HttpResult的方法，可以获取完整的响应信息
2. **异常处理**: 合理处理OkHttpException异常或检查HttpResult的success状态
3. **异步处理**: 对于耗时操作，使用异步方法避免阻塞
4. **配置调优**: 根据实际需求调整连接池和超时配置
5. **请求头管理**: 统一管理常用的请求头，如认证信息
6. **日志监控**: 在生产环境中适当调整日志级别

## 常见问题

### Q: 如何设置全局请求头？
A: 可以通过自定义OkHttpClient Bean来添加拦截器：

```java
@Configuration
public class OkHttpConfig {
    
    @Bean
    @Primary
    public OkHttpClient customOkHttpClient(OkHttpProperties properties) {
        return new OkHttpClient.Builder()
            .addInterceptor(chain -> {
                Request original = chain.request();
                Request.Builder requestBuilder = original.newBuilder()
                    .header("User-Agent", "MyApp/1.0")
                    .header("Accept", "application/json");
                return chain.proceed(requestBuilder.build());
            })
            // 其他配置...
            .build();
    }
}
```

### Q: 如何处理HTTPS证书问题？
A: 在测试环境中可以忽略SSL证书验证（生产环境不推荐）：

```java
@Bean
@Profile("dev")
public OkHttpClient unsafeOkHttpClient() {
    // 创建信任所有证书的TrustManager
    // 注意：仅用于开发环境
}
```

### Q: 如何自定义JSON序列化？
A: 可以自定义ObjectMapper Bean：

```java
@Bean
@Primary
public ObjectMapper customObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    return mapper;
}
```
