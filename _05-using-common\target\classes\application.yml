server:
  port: 8085

# 全局响应处理器配置
project91:
  global-response:
    enabled: true                    # 启用全局响应处理器
    exception-handler-enabled: true  # 启用全局异常处理器
    handle-string-response: true     # 处理String类型返回值
    handle-void-response: true       # 处理void类型返回值
    handle-primitive-response: true  # 处理基本数据类型返回值
    handle-collection-response: true # 处理集合类型返回值
    auto-trace-id: true             # 自动添加追踪ID
    trace-id-header: "X-Trace-Id"   # 追踪ID请求头名称
    add-server-info: true           # 添加服务器信息
    server-name: "using-common-server" # 服务器名称
    api-version: "v1.0"             # API版本
    exclude-packages:               # 排除的包路径
      - "springfox.documentation"
      - "org.springframework.boot.actuate"
    exclude-class-patterns:         # 排除的类名模式
      - "ErrorController"
      - "SwaggerController"

# 日志配置
logging:
  level:
    com.example: DEBUG
    lhx.project91: DEBUG
    root: INFO
